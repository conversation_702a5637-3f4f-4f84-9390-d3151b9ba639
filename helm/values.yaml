namespace: ""
cloudProvider: "gcp"
platform:
  env: "dev"
  settings:
    AWS_S3_ENABLE: "false"
    AI_JWT_SECRET: ""
    AWS_S3_REGION: ""
    AWS_S3_BUCKET: ""
    AWS_S3_ACCESS_KEY_ID: ""
    AWS_S3_SECRET_ACCESS_KEY: ""
    AWS_SES_REGION: ""
    AWS_SES_ACCESS_KEY_ID: ""
    AWS_SES_SECRET_ACCESS_KEY: ""
    AI_STORE_PATH: /opt/ai-store/files
    AI_DATA_URL: ""
    SITE_URL: ""
    AI_WEB2_IPSTACK_ACCESS_KEY: ""
    CAPTCHA_SECRET_CF: ""
    OPEN_AI_KEY: ""
    AI_WEB3_CHAIN_ID: "8453"
    AI_WEB3_RPC_URL: ""
    AI_WEB3_ACHIEVEMENT_MANAGER_CONTRACT_ADDRESS: ""
    AI_WEB3_DSP_REWARD_CREDENTIALS_PK: ""
    AI_WEB3_ACHIEVEMENT_ON_CONTRACT_ADDRESS: ""
    AI_WEB3_DSP_REWARD_CONTRACT_ADDRESS: "0x57F4Bc382d39fE0F9aD3D91d4B33A23Fae234445"
    AI_WEB3_DSP_REWARD_CHAIN_ID: "313313"
    AI_WEB3_DSP_REWARD_GRAPHQL_URL: "http://**************:8000/subgraphs/name/DSPRewardSahara"
    AI_WEB3_DSP_REWARD_RPC_URL: "https://testnet.saharalabs.ai"
    GOOGLE_RECAPTCHA_SECRET_V3: ""
    GOOGLE_RECAPTCHA_SECRET_V2: ""
    AWS_ACCESS_KEY_ID: ""
    AWS_SECRTE_ACCESS_KEY: ""
    KAFKA_SASL_ENABLE: "true"
    KAFKA_SERVERS: ""
    WORKLOAD_LIMIT_PLATFORM_IP_DAILY: "99999999"
    WORKLOAD_LIMIT_PLATFORM_USER_DAILY: 30
    CAMPFIRE_MACHINE_REVIEW_URL: ""
    CAMPFIRE_MACHINE_REVIEW_KEY: ""
    ONCHAIN_GRAPHQL_ENDPOINT: ""
    ONCHAIN_GRAPHQL_ENDPOINT2: ""
    DATABASE_ONCHAIN: ""
    DB_SCHEMA_ONCHAIN: ""
    DB_USER_ONCHAIN: ""
    DB_PASS_ONCHAIN: ""
    ACHIEVEMENT_PROGRESS_ONCHAIN_BATCH_SIZE: ""
    ACHIEVEMENT_ORACLE_DISABLED: ""
    OTEL_EXPORTER_OTLP_ENDPOINT: ""
    OTEL_EXPORTER_OTLP_PROTOCOL: ""
    OTEL_LOGS_EXPORTER: ""
    OTEL_RESOURCE_ATTRIBUTES: ""
    OTEL_EXPORTER_OTLP_HTTP_ENDPOINT: ""
    INTERNAL_API_KEY: ""
    AUTHENTICATION_URL: ""

  database:
    url: ""
    user: ""
    password: ""

  redis:
    host: ""
    port: ""
    password: ""
    ssl: "false"

  cluster:
    name: ""
    requiredContactPointNr: "2"

  resource:
    limits:
      memory: 4096Mi
    requests:
      cpu: 3
      memory: 4096Mi

javaOptions:
  maxHeapSize: 4g
  initialHeapSize: 2g

replicaCount: 3

image:
  repository: ""
  tag: ""
  pullPolicy: IfNotPresent

imagePullSecrets: [ ]
nameOverride: ""
fullnameOverride: ""

serviceAccount:
  # Specifies whether a service account should be created
  create: true
  # Automatically mount a ServiceAccount's API credentials?
  automount: true
  # Annotations to add to the service account
  annotations: { }
  # The name of the service account to use.
  # If not set and create is true, a name is generated using the fullname template
  name: ""

autoscaling:
  enabled: true
  minReplicas: 3
  maxReplicas: 5
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

# Additional volumes on the output Deployment definition.
volumes: [ ]
# - name: foo
#   secret:
#     secretName: mysecret
#     optional: false

# Additional volumeMounts on the output Deployment definition.
volumeMounts: [ ]
# - name: foo
#   mountPath: "/etc/foo"
#   readOnly: true

nodeSelector: { }

tolerations: [ ]

affinity: { }

efs:
  fileSystemId: ""
  accessPointId: ""
  capacity: 10Gi

service:
  type: ClusterIP
  port: 8080

ingress:
  enabled: true
  className: "alb"
  annotations:
    alb.ingress.kubernetes.io/scheme: internet-facing
    alb.ingress.kubernetes.io/target-type: ip
    alb.ingress.kubernetes.io/certificate-arn: ""
    alb.ingress.kubernetes.io/listen-ports: '[{"HTTP": 80}, {"HTTPS":443}]'
    alb.ingress.kubernetes.io/actions.ssl-redirect: '{"Type": "redirect", "RedirectConfig": { "Protocol": "HTTPS", "Port": "443", "StatusCode": "HTTP_301"}}'
    alb.ingress.kubernetes.io/healthcheck-path: /api/ping/alive
  hosts:
    - paths:
        - path: /*
          pathType: ImplementationSpecific
  tls: [ ]

