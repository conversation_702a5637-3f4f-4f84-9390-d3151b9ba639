USER-SUPPLIED VALUES:
affinity: {}
autoscaling:
  enabled: true
  maxReplicas: 5
  minReplicas: 3
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80
env:
  AI_WEB3_DSP_REWARD_CONTRACT_ADDRESS: 0x57F4Bc382d39fE0F9aD3D91d4B33A23Fae234445
filestore:
  capacity: 10Gi
fullnameOverride: ""
image:
  pullPolicy: IfNotPresent
  repository: us-west2-docker.pkg.dev/sahara-marketpla-1721186526493/docker-repo/campfire-staging
  tag: v1.0.193
imagePullSecrets: []
ingress:
  annotations:
    kubernetes.io/ingress.class: gce
    networking.gke.io/managed-certificates: managed-cert
    networking.gke.io/v1beta1.FrontendConfig: frontend-config
  enabled: false
  hosts:
  - host: gcp.saharaa.info
    paths:
    - path: /machine-review/api/v1/pipelines/*
      serviceName: campfire-machine-review-api
      servicePort: 8888
    - path: /machine-review/api/v1/callback/*
      serviceName: campfire-machine-review-api
      servicePort: 8888
    - path: /*
      serviceName: campfire-preprod-campfire-platform
      servicePort: 8080
javaOptions:
  initialHeapSize: 2g
  maxHeapSize: 3g
nameOverride: ""
namespace: default
nodeSelector: {}
platform:
  cloudProvider: gcp
  cluster:
    name: ai-dev-cluster
    requiredContactPointNr: "1"
  database:
    password: saharalabs@2024SH
    url: jdbc:postgresql://**************:5432/dev
    user: postgres
  env: dev
  redis:
    host: campfire-platform-redis-master
    password: 81aa23011e9f
    port: "6379"
    ssl: "false"
  resource:
    limits:
      cpu: 3
      memory: 4096Mi
    requests:
      cpu: 3
      memory: 4096Mi
  settings:
    ACHIEVEMENT_PROGRESS_ONCHAIN_BATCH_SIZE: "20"
    AI_DATA_URL: ""
    AI_JWT_SECRET: campfire-stagin-secAbcd!abb$1234aWLeZtYvX
    AI_JWT_TTL: 43200
    AI_STORE_PATH: /opt/ai-store/files
    AI_WEB2_IPSTACK_ACCESS_KEY: ********************************
    AI_WEB3_ACHIEVEMENT_MANAGER_CONTRACT_ADDRESS: 0xFbB5c6f63d905d1f04715c7EF81dA4fD5Dc0A8D5
    AI_WEB3_DSP_REWARD_CONTRACT_ADDRESS: 0x57F4Bc382d39fE0F9aD3D91d4B33A23Fae234445
    AI_WEB3_DSP_REWARD_CREDENTIALS_PK: 0x3295915f2980b634e2fd3980936b9dbc4d4a95daf8db694dede8f3a6b1f0aecd
    AI_WEB3_DSP_REWARD_GRAPHQL_URL: http://**************:8000/subgraphs/name/DSPRewardSahara
    AI_WEB3_RPC_URL: https://rpc2.saharaa.info
    AUTHENTICATION_URL: http://sahara-authentication-backend
    AWS_SES_ACCESS_KEY_ID: ********************
    AWS_SES_REGION: us-west-2
    AWS_SES_SECRET_ACCESS_KEY: SsJ76UxBsEGLrPmEnwDMS/HCAMSd7Tf5MW1XV6Qy
    CAPTCHA_SECRET_CF: 0x4AAAAAAAOowOtrU1KElHsSYebL4WPUsXM
    CLOUD_PROVIDER: gcp
    DATABASE_ONCHAIN: ********************************************
    DB_PASS_ONCHAIN: Ez[|RKH<f+dgec2~
    DB_SCHEMA_ONCHAIN: sgd9
    DB_USER_ONCHAIN: postgres
    GCS_BUCKET: campfire-dev-store
    GOOGLE_RECAPTCHA_SECRET_V2: 6LeFkRoqAAAAAMTCAM0L28wWHGVw3lAm_dEof0xW
    GOOGLE_RECAPTCHA_SECRET_V3: 6Lc-DhEqAAAAAAto1-w1cWGmmReJGmaDe2gEUiXO
    INTERNAL_API_KEY: h2XNQ4GgP5e+LZ8VdjJrj8v5xuAq0L8RS7A3UKN+ixFf43jUAb1iA93whPtRbKRe9pYmOWZSRN3mskSLeRZcZQ==
    KAFKA_SERVERS: my-cluster-kafka-bootstrap.kafka.svc.cluster.local:9092
    ONCHAIN_GRAPHQL_ENDPOINT: https://graph.saharaa.info/subgraphs/name/DataServiceMar14
    ONCHAIN_GRAPHQL_ENDPOINT2: https://graph.saharaa.info/subgraphs/name/TierStake
    OPEN_AI_KEY: ***************************************************
    OTEL_EXPORTER_OTLP_ENDPOINT: http://***********:4317
    OTEL_EXPORTER_OTLP_HTTP_ENDPOINT: http://***********:4318/v1/metrics
    OTEL_EXPORTER_OTLP_PROTOCOL: grpc
    OTEL_LOGS_EXPORTER: otlp
    OTEL_RESOURCE_ATTRIBUTES: service.name=campfire-staging
    SITE_URL: https://gcp-dev.saharaa.info
    STATSIG_SERVER_SECRET: secret-Wzdlqg8BdlVuCxXau3CXCMlyLMMdZVa9Bv9JxeswwQ2
    WORKLOAD_LIMIT_PLATFORM_IP_DAILY: 75
    WORKLOAD_LIMIT_PLATFORM_USER_DAILY: 25
replicaCount: 2
service:
  port: 8080
  type: ClusterIP
serviceAccount:
  gcpServiceAccount: <EMAIL>
  name: platform-preprod-ksa
tolerations: []
volumeMounts: []
volumes: []
