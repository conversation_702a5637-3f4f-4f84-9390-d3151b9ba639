package ai.saharaa.services;

import static java.util.stream.Collectors.toList;
import static org.assertj.core.api.Assertions.assertThat;

import ai.saharaa.daos.BatchNDADao;
import ai.saharaa.daos.ExamSessionDao;
import ai.saharaa.daos.TaskQuestionDao;
import ai.saharaa.dto.batch.BatchDetailsDTO;
import ai.saharaa.dto.batch.UpdateBatchDTO;
import ai.saharaa.dto.job.JobInvitationDTO;
import ai.saharaa.dto.job.JobInviteDTO;
import ai.saharaa.dto.job.JobUserInviteDTO;
import ai.saharaa.dto.pre.CreatePreTaskDTO;
import ai.saharaa.model.*;
import ai.saharaa.utils.Constants;
import ai.saharaa.utils.DateUtils;
import ai.saharaa.utils.TestUtils;
import com.baomidou.mybatisplus.core.metadata.IPage;
import io.zonky.test.db.AutoConfigureEmbeddedDatabase;
import java.time.Duration;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit.jupiter.SpringExtension;
import org.springframework.transaction.annotation.Transactional;

@SpringBootTest
@ExtendWith(SpringExtension.class)
@Transactional
@AutoConfigureEmbeddedDatabase
class BatchServiceTest {

  private Logger log = org.slf4j.LoggerFactory.getLogger(BatchServiceTest.class);

  @Autowired
  private UserService userService;

  @Autowired
  BatchService batchService;

  @Autowired
  BatchNDADao batchNDADao;

  @Autowired
  ProjectService projectService;

  @Autowired
  PreTaskService preTaskService;

  @Autowired
  JobService jobService;

  @Autowired
  JobSessionService jobSessionService;

  @Autowired
  JobUserService jobUserService;

  @Autowired
  BatchSampleService batchSampleService;

  @Autowired
  TaskQuestionDao taskQuestionDao;

  @Autowired
  TaskService taskService;

  @Autowired
  TaskListService taskListService;

  @Autowired
  ExamSessionDao examSessionDao;

  @Autowired
  TestUtils testUtils;

  User am;
  User nodeUser;
  User qm;
  User labeler;
  Node node;
  Project project;

  @BeforeEach
  void setup() {
    var users = testUtils.createUsers(
        List.of(Constants.ROLE_ACCOUNT_MANAGER, Constants.ROLE_ACCOUNT_MANAGER), "am");
    am = users.get(0);

    var nodeUsers = testUtils.createUsers(
        List.of(Constants.ROLE_NODE_MANAGER, Constants.ROLE_NODE_MANAGER), "node");
    nodeUser = nodeUsers.get(0);
    node = testUtils.createNode("test", nodeUser.getId());

    var qmUsers = testUtils.createUsers(
        List.of(Constants.ROLE_QUEUE_MANAGER, Constants.ROLE_NODE_MANAGER), "qm");
    qm = qmUsers.get(0);

    var labelerUsers =
        testUtils.createUsers(List.of(Constants.ROLE_USER, Constants.ROLE_USER), "labeler");
    labeler = labelerUsers.get(0);
    testUtils.joinNode(node, Arrays.asList(labeler));

    project = testUtils.createTrainerTeamProject(am.getId());
  }

  @Test
  void ensureBatchCreated() {
    var batch = testUtils.createSimpleBatch(project);

    assertThat(batch.getId()).isNotNull();
    assertThat(batch.getSummary()).isEqualTo("test summary");
    assertThat(batch.getSummaryForAnnotator()).isEqualTo("test summary-for-annotator");
    assertThat(batch.getSummaryForReviewer()).isEqualTo("test summary-for-reviewer");
    assertThat(batch.getDeleted()).isEqualTo(false);
    assertThat(batch.getDescription()).isEqualTo("test description");
    assertThat(batch.getOwnerId()).isEqualTo(am.getId());
    assertThat(batch.getProjectId()).isEqualTo(project.getId());
  }

  private void createBatchNDA(Batch batch) {
    batchNDADao.batchUpdateNda(
        batch.getId(),
        List.of(),
        List.of(),
        List.of(
            BatchNDA.builder()
                .title("test nda 0")
                .content("test nda content 0")
                .batchId(batch.getId())
                .ownerId(batch.getOwnerId())
                .build(),
            BatchNDA.builder()
                .title("test nda 1")
                .content("test nda content 1")
                .batchId(batch.getId())
                .ownerId(batch.getOwnerId())
                .build(),
            BatchNDA.builder()
                .title("test nda 2")
                .content("test nda content 2")
                .batchId(batch.getId())
                .ownerId(batch.getOwnerId())
                .build()));
  }

  @Test
  void ensureBatchNDACreated() {
    var batch = testUtils.createSimpleBatch(project);
    var oldNDAs = batchNDADao.getNDAsByBatch(batch.getId());
    assertThat(oldNDAs).isNullOrEmpty();
    createBatchNDA(batch);
    var ndaCount = batchNDADao.getNdaCountByBatchId(batch.getId());
    var ndas = batchNDADao.getNDAsByBatch(batch.getId());
    assertThat(ndaCount).isNotZero();
    assertThat(ndas).isNotEmpty();
  }

  @Test
  void ensureBatchNDAUpdated() {
    var batch = testUtils.createSimpleBatch(project);
    createBatchNDA(batch);
    var ndas = batchNDADao.getNDAsByBatch(batch.getId());
    ndas.forEach(nda -> nda.setTitle("new nda title"));
    batchNDADao.batchUpdateNda(batch.getId(), List.of(), ndas, List.of());
    ndas = batchNDADao.getNDAsByBatch(batch.getId());
    ndas.forEach(nda -> assertThat(nda.getTitle()).isEqualTo("new nda title"));
  }

  @Test
  void ensureBatchNDADeletedByBatchUpdate() {
    var batch = testUtils.createSimpleBatch(project);
    createBatchNDA(batch);
    var ndas = batchNDADao.getNDAsByBatch(batch.getId());
    var oldCount = ndas.size();
    var nda = ndas.get(0);
    batchNDADao.batchUpdateNda(batch.getId(), List.of(nda.getId()), List.of(), List.of());
    ndas = batchNDADao.getNDAsByBatch(batch.getId());
    var newCount = ndas.size();
    assertThat(oldCount - newCount).isOne();
  }

  @Test
  void ensureBatchNDADeleted() {
    var batch = testUtils.createSimpleBatch(project);
    createBatchNDA(batch);
    batchNDADao.getNDAsByBatch(batch.getId()).forEach(nda -> {
      batchNDADao.deleteById(nda.getId());
    });

    var ndas = batchNDADao.getNDAsByBatch(batch.getId());
    assertThat(ndas).isEmpty();
  }

  @Test
  void test2() {
    var u2 = userService.getUserById(am.getId());
    assertThat(u2).isPresent();
    assertThat(u2.get().getFirstName()).isEqualTo("am-0-account_manager");
  }

  private Batch createBatch() {
    Batch exam = Batch.builder()
        .ownerId(am.getId())
        .name("exam")
        .dataType("image")
        .projectId(project.getId())
        .taskType(Batch.TaskType.EXAM)
        .build();
    batchService.createBatchV2(exam, Collections.EMPTY_LIST);
    log.info("exam: {}", exam);

    Batch batch = Batch.builder()
        .ownerId(am.getId())
        .name("task")
        .dataType("image")
        .projectId(project.getId())
        .taskType(Batch.TaskType.TASK)
        .userType(Batch.UserType.INTERNAL_QA_TEAM)
        .difficulty(Batch.CourseDifficulty.Expert)
        .deadline(DateUtils.now())
        .build();
    batchService.createBatchV2(
        batch,
        Arrays.asList(BatchAccessRequirement.builder()
            .relationId(exam.getId())
            .type(BatchAccessRequirement.Type.EXAM)
            .build()));
    log.info("batch: {}", batch);

    return batch;
  }

  @Test
  void testCreateBatchV2() {
    createBatch();
  }

  @Test
  void testUpdateV2() {
    Batch batch = createBatch();

    UpdateBatchDTO update = UpdateBatchDTO.builder()
        .name("new name")
        .userType(Batch.UserType.EXTERNAL_LABELER)
        .build();
    batchService.updateBatchDataV2(batch.getId(), update, batch);
  }

  @Test
  void testCopyV2() {
    Batch originalBatch = createBatch();
    batchService.copyBatchV2(originalBatch);
  }

  @Test
  void testCreateBatchV2Pretask() {
    Batch batch = createBatch();
    BatchDetailsDTO batchDetails = this.batchService.getBatchDetails(batch.getId());

    // qm
    CreatePreTaskDTO createPreTaskDTO = CreatePreTaskDTO.builder()
        .batchId(batch.getId())
        .preTaskDeadline(DateUtils.add(DateUtils.now(), Duration.ofDays(1)))
        .inviteAcceptanceDeadline(DateUtils.add(DateUtils.now(), Duration.ofDays(1)))
        .examIds(batchDetails
            .getAccessRequirements()
            .map(BatchAccessRequirement::getRelationId)
            .collect(toList()))
        .invitationType(JobInvitation.InvitationType.PRE_TASK)
        .nodeIds(Arrays.asList(node.getId()))
        .build();
    var preTask = preTaskService.createPreTaskV2(createPreTaskDTO, qm.getId());
    log.info("preTask: {}", preTask);

    // nm
    // FIXME I don’t know how to fill in the user ID here, so it’s tentatively decided like this
    IPage<JobInvitationDTO> jobInvitations =
        jobService.getJobInvitations(node.getId(), 1, 10, "pending", node.getNodeManagerId());
    log.info("jobInvitations: {}", jobInvitations.getRecords());
    assertThat(jobInvitations.getRecords()).isNotEmpty();

    JobInvitation invitation = jobInvitations.getRecords().get(0).getInvitation();
    Boolean dealWithJobInvitationRes = jobService.dealWithJobInvitation(
        nodeUser.getId(), invitation.getId(), invitation.getStatus());
    log.info("dealWithJobInvitation: {}", dealWithJobInvitationRes);
    assertThat(dealWithJobInvitationRes).isTrue();

    JobUserInviteDTO body = JobUserInviteDTO.builder()
        .jobId(invitation.getJobId())
        .userIdList(Arrays.asList(labeler.getId()))
        .build();
    Boolean doInviteUserToJobRes = jobUserService.doInviteUserToJob(body, null, labeler.getId());
    log.info("doInviteUserToJob: {}", doInviteUserToJobRes);

    // labeler
    IPage<JobInviteDTO> myInvites = jobUserService.getMyInvites(1, 10, labeler.getId());
    log.info("myInvites: {}", myInvites.getRecords());
    assertThat(myInvites.getRecords()).isNotEmpty();

    JobInviteDTO jobInvite = myInvites.getRecords().get(0);
    Boolean acceptInviteExamRes =
        jobSessionService.acceptInviteExam(jobInvite.getInvite().getId(), labeler.getId(), true);
    log.info("acceptInviteExam: {}", acceptInviteExamRes);
  }

  @Test
  void ensureBatchNDACreatedV2() {
    var batch = createBatch();
    var oldNDAs = batchNDADao.getNDAsByBatch(batch.getId());
    assertThat(oldNDAs).isNullOrEmpty();
    createBatchNDA(batch);
    var ndaCount = batchNDADao.getNdaCountByBatchId(batch.getId());
    var ndas = batchNDADao.getNDAsByBatch(batch.getId());
    assertThat(ndaCount).isNotZero();
    assertThat(ndas).isNotEmpty();
  }

  @Test
  void ensureBatchNDADeletedV2() {
    var batch = createBatch();
    createBatchNDA(batch);
    batchNDADao.getNDAsByBatch(batch.getId()).forEach(nda -> {
      batchNDADao.deleteById(nda.getId());
    });

    var ndas = batchNDADao.getNDAsByBatch(batch.getId());
    assertThat(ndas).isEmpty();
  }

  @Test
  void testCreateExamV2() {
    this.testUtils.createExam(project.getId(), am.getId());
  }
}
