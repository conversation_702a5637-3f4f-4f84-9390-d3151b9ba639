package ai.saharaa.services;

import static org.assertj.core.api.Assertions.fail;

import ai.saharaa.daos.JobTaskDao;
import ai.saharaa.daos.TaskListDao;
import ai.saharaa.daos.TaskQuestionDao;
import ai.saharaa.daos.season.SeasonDao;
import ai.saharaa.dto.job.QuestionReviewDTO;
import ai.saharaa.dto.job.SubmitAnswerDTO;
import ai.saharaa.dto.job.SubmitAnswersItemDTO;
import ai.saharaa.dto.job.SubmitReviewDTO;
import ai.saharaa.dto.job.TakeJobResultDTO;
import ai.saharaa.dto.project.ProjectDetailsV2DTO;
import ai.saharaa.dto.task.AnswerDetailsDTO;
import ai.saharaa.dto.task.TaskQuestionDetailsDTO;
import ai.saharaa.enums.Knowledge;
import ai.saharaa.model.Batch;
import ai.saharaa.model.BatchSample;
import ai.saharaa.model.Job;
import ai.saharaa.model.JobUser;
import ai.saharaa.model.Project;
import ai.saharaa.model.Task;
import ai.saharaa.model.TaskList;
import ai.saharaa.model.TaskQuestion;
import ai.saharaa.model.User;
import ai.saharaa.model.season.Season;
import ai.saharaa.services.season.RuleService;
import ai.saharaa.utils.Constants;
import ai.saharaa.utils.DateUtils;
import ai.saharaa.utils.ObjectUtils;
import ai.saharaa.utils.TestUtils;
import com.fasterxml.jackson.databind.ObjectMapper;
import io.zonky.test.db.AutoConfigureEmbeddedDatabase;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import org.databene.contiperf.PerfTest;
import org.databene.contiperf.junit.ContiPerfRule;
import org.junit.Before;
import org.junit.Rule;
import org.junit.Test;
import org.junit.jupiter.api.TestInstance;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest
@RunWith(SpringRunner.class)
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
@AutoConfigureEmbeddedDatabase
public class IndividualTest {

  private Logger log = org.slf4j.LoggerFactory.getLogger(IndividualTest.class);

  @Autowired
  TestUtils testUtils;

  @Autowired
  ProjectService projectService;

  @Autowired
  JobService jobService;

  @Autowired
  SeasonDao seasonDao;

  @Autowired
  JobUserService jobUserService;

  @Autowired
  JobTaskService jobTaskService;

  @Autowired
  ReviewSessionService reviewSessionService;

  @Autowired
  TaskSessionService taskSessionService;

  @Autowired
  BatchSampleService batchSampleService;

  @Autowired
  JobTaskDao jobTaskDao;

  @Autowired
  TaskListService taskListService;

  @Autowired
  TaskListDao taskListDao;

  @Autowired
  TaskService taskService;

  @Autowired
  TaskQuestionDao taskQuestionDao;

  @Autowired
  TaskQuestionService taskQuestionService;

  @Autowired
  BatchService batchService;

  @Rule
  public ContiPerfRule i = new ContiPerfRule();

  static volatile int alreadyInit = 0; // 0 not start, 1 starting, 2 started
  static volatile int alreadySummary = 0;
  ProjectDetailsV2DTO project;
  Batch taskBatch;
  Job job;
  User creatorUsers;
  Thread consumer;
  Map<Long, User> individualUsers = new HashMap<>();
  Map<Long, Long> theadUserMap = new ConcurrentHashMap<>();

  private final Lock lock = new ReentrantLock();
  private final Lock lock2 = new ReentrantLock();
  private final Lock lock3 = new ReentrantLock();

  private static final Integer taskCount = 1000;
  private Integer maxReviewCount = taskCount * 3;
  private Integer reviewCounter = 0;
  private List<Integer> perfListA1 = new ArrayList<>();
  private List<Integer> perfListA2 = new ArrayList<>();
  private List<Integer> perfListB1 = new ArrayList<>();
  private List<Integer> perfListB2 = new ArrayList<>();
  private static final Integer jobUserCount = 100;

  @Before
  public void testCreateClient() {
    if (alreadyInit == 0) {
      var mapper = new ObjectMapper();
      log.info("Do Start! alreadyInit:" + alreadyInit);
      alreadyInit = 1;
      int[] members = new int[jobUserCount];
      Arrays.fill(members, Constants.ROLE_EXTERNAL_USER);
      var memberList = Arrays.stream(members).boxed().toList();
      log.info("Start!!! 2" + mapper.writeValueAsString(memberList));
      var individualUserPre = testUtils.createUsers(memberList, "individual");
      for (User u : individualUserPre) {
        individualUsers.put(u.getId(), u);
      }
      creatorUsers = testUtils
          .createUsers(List.of(Constants.ROLE_ACCOUNT_MANAGER), "creator")
          .first();

      project = createProjectForIndividuals(creatorUsers.getId());
      log.info("project: " + project.getProject().getName());
      taskBatch = testUtils.createTaskBatchForIndividuals(project.getProject());
      var taskLists = taskListDao.listTaskListsByBatchId(taskBatch.getId());
      var annoTaskList = taskLists.stream()
          .filter(tl -> tl.getListType().equals(TaskList.TaskListType.ANNOTATION))
          .findFirst()
          .get();
      var labelTaskList = taskLists.stream()
          .filter(tl -> tl.getListType().equals(TaskList.TaskListType.LABEL))
          .findFirst()
          .get();
      var resources =
          testUtils.createTextResourcesLarge(project.getProject().getAccountManagerId(), taskCount);
      log.info("resources: " + resources.size());
      var labelingTasks = resources
          .map(r -> taskService.createTask(Task.builder()
              .taskListId(annoTaskList.getId())
              .resourceId(r.getId())
              .ownerId(project.getProject().getAccountManagerId())
              .taskType(Task.TaskType.TASK)
              .sort(r.getId().intValue())
              .build()))
          .toList();

      taskQuestionDao.createTaskQuestion(TaskQuestion.builder()
          .taskId(labelTaskList.getId())
          .ownerId(creatorUsers.getId())
          .groupId(null)
          .question("are you ok?")
          .questionType(TaskQuestion.QuestionType.TEXT)
          .details(ObjectUtils.toJson(TaskQuestionDetailsDTO.builder()
              .questionType(TaskQuestion.QuestionType.TEXT.getValue())
              .conditions(List.of())
              .explain("")
              .text("")
              .maxLength(0)
              .placeholder("")
              .grammarIssues("{}")
              .build()))
          .answer("")
          .answerRequired(false)
          .sort(0)
          .build());

      var exampleResources =
          testUtils.createTextResources(project.getProject().getAccountManagerId(), 1);
      var samples = exampleResources
          .map(r -> BatchSample.builder()
              .batchId(taskBatch.getId())
              .resourceId(r.getId())
              .ownerId(project.getProject().getAccountManagerId())
              .build())
          .toList();
      batchSampleService.createBatchSamples(samples, labelTaskList.getId());

      jobService.assignJobToExternalBatchJob(taskBatch);
      batchService.launchBatch(taskBatch);

      job = jobService.getJobsByBatchId(taskBatch.getId()).first();
      job.setCreatedAt(DateUtils.add(DateUtils.now(), Duration.ofHours(-25)));
      jobService.updateById(job);
      log.info("job created !: " + mapper.writeValueAsString(job));
      seasonDao.create(Season.builder()
          .name("seasonName")
          .ruleVersion(RuleService.Version.MARCH)
          .startedAt(DateUtils.add(DateUtils.now(), Duration.ofHours(-1)))
          .endedAt(DateUtils.add(DateUtils.now(), Duration.ofDays(200)))
          .build());
      alreadyInit = 2;
    }
  }

  private void takeReviewAndAnswerAndSubmit(JobUser currentJobUser) {
    lock2.lock();
    try {
      var timerStart = DateUtils.now();
      //            log.info("start takeReviewAndAnswerAndSubmit !");
      var mapper = new ObjectMapper();
      var batchCreateAt = DateUtils.now();
      var jobs = jobTaskService.takeReviewJobsForUser2(
          currentJobUser.getTaskListSessionId(),
          currentJobUser.getUserId(),
          taskBatch.getReviewingRequiredDatapoint(),
          taskBatch.getReviewingTimesReviewPerDatapoint());
      if (jobs.isEmpty()) {
        return;
      } else {
        log.info("take how many review jobs: " + jobs.size());
      }
      var timerMiddle = DateUtils.now();
      var reviewSessions = jobs.stream()
          .map(j -> reviewSessionService.createReviewSession(currentJobUser, j))
          .toList();
      var res = reviewSessions
          .map(session -> TakeJobResultDTO.builder()
              .taskSession(taskSessionService
                  .getTaskSessionById(session.getTaskSessionId())
                  .orElse(null))
              .reviewSession(session)
              .resource(taskService.getResourceByTaskId(session.getTaskId()).orElse(null))
              .build())
          .toList();
      var submitReviews = res.stream()
          .map(r -> SubmitReviewDTO.builder()
              .reviewSessionId(r.getReviewSession().getId())
              .taskSessionId(r.getTaskSession().getId())
              .jobId(r.getTaskSession().getJobId())
              .approve(true)
              .time(12L)
              .questionReviews(Arrays.asList(
                  QuestionReviewDTO.builder().approve(true).questionId(1L).build()))
              .build())
          .toList();

      var submittedReviewSessions = reviewSessionService.handleSubmitReviews(
          submitReviews, taskBatch, this.job, currentJobUser.getUserId(), new ArrayList<>());
      log.info("submittedReviewSessions: " + submittedReviewSessions.size() + "  reviewCounter:"
          + reviewCounter);
      var timerEnd = DateUtils.now();

      reviewCounter += submittedReviewSessions.size();
      perfListB1.add((int) (timerMiddle.getTime() - timerStart.getTime()));
      perfListB2.add((int) (timerEnd.getTime() - timerMiddle.getTime()));
      if (reviewCounter >= maxReviewCount) {
        log.info("perfList A1: " + mapper.writeValueAsString(perfListA1));
        log.info("perfList A2: " + mapper.writeValueAsString(perfListA2));
        log.info("perfList B1: " + mapper.writeValueAsString(perfListB1));
        log.info("perfList B2: " + mapper.writeValueAsString(perfListB2));
        fail("end");
      }
    } catch (Exception e) {
      e.printStackTrace();
    } finally {
      lock2.unlock();
    }
  }

  private void takeJobAndAnswerAndSubmit(JobUser currentJobUser) {
    lock.lock();
    try {
      var timerStart = DateUtils.now();
      //            log.info("start takeJobAndAnswerAndSubmit !");
      var mapper = new ObjectMapper();
      var batchCreateAt = DateUtils.now();
      var jobs = jobTaskService.takeJobForJobUserAvoidSkipped(
          currentJobUser.getTaskListSessionId(), currentJobUser.getUserId(), 1, 10);
      if (jobs.isEmpty()) {
        return;
      } else {
        log.info("take how many labeling jobs: " + jobs.size());
      }
      var timerMiddle = DateUtils.now();
      JobUser finalCurrentJobUser = currentJobUser;
      var fetchedJobs = jobs.map(newJob -> taskSessionService.createTaskSession(
              finalCurrentJobUser, newJob, JobUser.JobUserRole.LABELER, batchCreateAt))
          .toList();

      var answers = fetchedJobs.stream()
          .map(ts -> {
            var questionAnswers = Arrays.asList(AnswerDetailsDTO.builder()
                .questionId(1L)
                .answer("123")
                .explanation("")
                .build());
            var ans =
                SubmitAnswerDTO.builder().feedback("").answers(questionAnswers).build();
            return SubmitAnswersItemDTO.builder()
                .taskSession(ts)
                .time(12L)
                .submitAnswer(ans)
                .build();
          })
          .toList();

      var submittedTaskSessions = taskSessionService.submitAnswers(
          answers,
          taskBatch,
          new ArrayList<>(),
          batchService.getBatchSettingByBatchId(taskBatch.getId()),
          false);
      log.info("submittedTaskSessions: " + submittedTaskSessions.size());
      var timerEnd = DateUtils.now();

      perfListA1.add((int) (timerMiddle.getTime() - timerStart.getTime()));
      perfListA2.add((int) (timerEnd.getTime() - timerMiddle.getTime()));
    } finally {
      lock.unlock();
    }
  }

  @Test
  @PerfTest(threads = 100, duration = 900000, rampUp = 100)
  public void doTest() {
    if (alreadyInit != 2) {
      Thread.sleep(2000L);
      return;
    }
    long currentThreadId = Thread.currentThread().getId();
    //        log.info("doTest: alreadyInit:" + alreadyInit);
    var mapper = new ObjectMapper();
    try {
      User currentUser = null;
      if (this.theadUserMap.containsKey(currentThreadId)) {
        currentUser = this.individualUsers.get(this.theadUserMap.get(currentThreadId));
      } else {
        var newUserId = this.individualUsers.keySet().stream()
            .filter(k -> !this.theadUserMap.containsValue(k))
            .findFirst();
        if (newUserId.isPresent()) {
          this.theadUserMap.set(currentThreadId, newUserId.get());
          currentUser = this.individualUsers.get(this.theadUserMap.get(currentThreadId));
        }
      }
      if (currentUser != null) {
        var jobUser =
            this.jobUserService.getJobUserByJobAndUserId(this.job.getId(), currentUser.getId());
        JobUser currentJobUser = null;
        if (jobUser.isPresent()) {
          currentJobUser = jobUser.get();
        } else {
          lock3.lock();
          try {
            var jobUserNew = this.jobService.joinIndividualJob(this.job, currentUser.getId());
            currentJobUser = jobUserNew;
            log.info("jobUser: just joined " + mapper.writeValueAsString(jobUserNew));
          } finally {
            lock3.unlock();
          }
        }

        if (currentJobUser.getRole().equals(JobUser.JobUserRole.LABELER)) {
          takeJobAndAnswerAndSubmit(currentJobUser);
        } else if (currentJobUser.getRole().equals(JobUser.JobUserRole.REVIEWER)) {
          takeReviewAndAnswerAndSubmit(currentJobUser);
        }
      }

    } catch (Exception e) {
      e.printStackTrace();
    } finally {
      //            var jobUsers = this.jobUserService.getActiveJobUsers(this.job.getId());
      //            log.info("current job have how many users: " + jobUsers.size());
    }
  }

  private ProjectDetailsV2DTO createProjectForIndividuals(Long userId) {
    Project project = Project.builder()
        .name("project for individual")
        .description("project for individual")
        .requesterId(userId)
        .accountManagerId(userId)
        .memo("project memo")
        .projectType(Project.ProjectType.INDIVIDUAL)
        .knowledge(Knowledge.ENGINEERING_MECHANICS.getValue())
        .createdAt(DateUtils.now())
        .updatedAt(DateUtils.now())
        .build();

    ProjectDetailsV2DTO projectV2 = projectService.createProjectV2(project, null);
    return projectV2;
  }
}
