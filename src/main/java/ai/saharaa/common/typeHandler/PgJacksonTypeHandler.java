package ai.saharaa.common.typeHandler;

import ai.saharaa.utils.BeanAccessor;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.lang.reflect.Field;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.postgresql.util.PGobject;

@MappedJdbcTypes(JdbcType.OTHER)
@MappedTypes(Object.class)
public class PgJacksonTypeHandler extends JacksonTypeHandler {

  public PgJacksonTypeHandler(Class<?> type, Field field) {
    super(type);
    setObjectMapper(springObjectMapperOrNew());
  }

  public PgJacksonTypeHandler(Class<?> type) {
    super(type);
    setObjectMapper(springObjectMapperOrNew());
  }

  private ObjectMapper springObjectMapperOrNew() {
    // Type handlers for now is not managed by Spring. So it does not automatically have access to
    // Spring's ObjectMapper.
    // When it does in the future, we should remove the BeanAccessor.
    // https://github.com/mybatis/spring/issues/493
    ObjectMapper objectMapper = BeanAccessor.getBean(ObjectMapper.class);
    return objectMapper != null ? objectMapper : new ObjectMapper();
  }

  // JacksonTypeHandler's setNonNullParameter implementation does not work with postgres. We have to
  // set a PGobject instead.
  @Override
  public void setNonNullParameter(PreparedStatement ps, int i, Object parameter, JdbcType jdbcType)
      throws SQLException {
    final PGobject jsonObject = new PGobject();

    jsonObject.setType("json");
    try {
      jsonObject.setValue(getObjectMapper().writeValueAsString(parameter));
    } catch (JsonProcessingException e) {
      throw new RuntimeException(e);
    }
    ps.setObject(i, jsonObject);
  }
}
