package ai.saharaa.services;

import static ai.saharaa.model.Batch.afterLaunchBatchStatusList;
import static java.util.stream.Collectors.toList;

import ai.saharaa.common.cache.IGlobalCache;
import ai.saharaa.config.AuditLogInterceptor;
import ai.saharaa.daos.BatchAccessRequirementDao;
import ai.saharaa.daos.BatchDao;
import ai.saharaa.daos.BatchInstructionHistoriesDao;
import ai.saharaa.daos.BatchNDADao;
import ai.saharaa.daos.BatchSampleDao;
import ai.saharaa.daos.HybridTaskResourceDao;
import ai.saharaa.daos.JobDao;
import ai.saharaa.daos.JobTaskDao;
import ai.saharaa.daos.QuestionAnswerDao;
import ai.saharaa.daos.QuestionGroupDao;
import ai.saharaa.daos.ResourceDao;
import ai.saharaa.daos.TaskDao;
import ai.saharaa.daos.TaskListDao;
import ai.saharaa.daos.TaskQuestionDao;
import ai.saharaa.daos.academy.AcademyDao;
import ai.saharaa.daos.newTasks.BatchRewardRecordDao;
import ai.saharaa.daos.newTasks.RewardTokenInfoDao;
import ai.saharaa.dto.batch.BatchDetailsDTO;
import ai.saharaa.dto.batch.RewardTokenDTO;
import ai.saharaa.dto.batch.UpdateBatchDTO;
import ai.saharaa.dto.task.TaskQuestionDetailsDTO;
import ai.saharaa.enums.CertificateCategory;
import ai.saharaa.enums.CertificateType;
import ai.saharaa.enums.TaskSessionAmReviewStatus;
import ai.saharaa.exception.AppException;
import ai.saharaa.mappers.AmAuditSessionMapper;
import ai.saharaa.mappers.BatchMapper;
import ai.saharaa.mappers.BatchSettingMapper;
import ai.saharaa.mappers.ClientAuditSessionMapper;
import ai.saharaa.mappers.JobMapper;
import ai.saharaa.mappers.TaskSessionMapper;
import ai.saharaa.model.AmAuditSession;
import ai.saharaa.model.AuditLog;
import ai.saharaa.model.Batch;
import ai.saharaa.model.BatchAccessRequirement;
import ai.saharaa.model.BatchInstructionHistories;
import ai.saharaa.model.BatchSample;
import ai.saharaa.model.BatchSetting;
import ai.saharaa.model.ClientAuditSession;
import ai.saharaa.model.HoneyPotBatch;
import ai.saharaa.model.HybridTaskResource;
import ai.saharaa.model.Job;
import ai.saharaa.model.PageResult;
import ai.saharaa.model.Project;
import ai.saharaa.model.QuestionAnswer;
import ai.saharaa.model.QuestionGroup;
import ai.saharaa.model.Resource;
import ai.saharaa.model.Task;
import ai.saharaa.model.TaskList;
import ai.saharaa.model.TaskQuestion;
import ai.saharaa.model.TaskSession;
import ai.saharaa.model.UserDetails;
import ai.saharaa.model.certificate.Certificate;
import ai.saharaa.model.certificate.UserCertificate;
import ai.saharaa.model.newTasks.BatchRewardRecord;
import ai.saharaa.model.newTasks.RewardTokenInfo;
import ai.saharaa.utils.Constants;
import ai.saharaa.utils.ControllerUtils;
import ai.saharaa.utils.DateUtils;
import ai.saharaa.utils.MockMultipartFile;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.github.yulichang.toolkit.JoinWrappers;
import com.google.common.base.Splitter;
import java.sql.Timestamp;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import manifold.rt.api.util.Pair;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
public class BatchService extends ServiceImpl<BatchMapper, Batch> {
  private final BatchMapper batchMapper;
  private final BatchSettingMapper batchSettingMapper;
  private final BatchDao batchDao;
  private final IGlobalCache redisCache;

  private final BatchNDADao batchNDADao;
  private final ResourceDao resourceDao;
  private final JobMapper jobMapper;
  private final JobDao jobDao;
  private final TaskDao taskDao;
  private final TaskSessionMapper taskSessionMapper;
  private final TaskListDao taskListDao;
  private final TaskService taskService;
  private final TaskQuestionDao taskQuestionDao;
  private final QuestionGroupDao questionGroupDao;
  private final QuestionAnswerDao questionAnswerDao;
  private final HybridTaskResourceDao hybridTaskResourceDao;
  private final AmAuditSessionMapper amAuditSessionMapper;
  private final ClientAuditSessionMapper clientAuditSessionMapper;
  private final JobTaskDao jobTaskDao;
  private final BatchSampleDao batchSampleDao;
  private final AuditLogService auditLogService;
  private final BatchInstructionHistoriesDao batchInstructionHistoriesDao;
  private final ObjectMapper objectMapper;
  private final NotificationService notificationService;
  private final BatchAccessRequirementDao batchAccessRequirementDao;
  private final ProjectService projectService;
  private final BatchSampleService batchSampleService;
  private final ResourceService resourceService;
  private final AcademyDao academyDao;

  private final VaultService vaultService;
  private final BatchRewardRecordDao batchRewardRecordDao;
  private final RewardTokenInfoDao rewardTokenInfoDao;

  public BatchService(
      BatchMapper batchMapper,
      JobDao jobDao,
      TaskDao taskDao,
      BatchSampleDao batchSampleDao,
      JobTaskDao jobTaskDao,
      TaskListDao taskListDao,
      TaskService taskService,
      TaskQuestionDao taskQuestionDao,
      QuestionAnswerDao questionAnswerDao,
      HybridTaskResourceDao hybridTaskResourceDao,
      JobMapper jobMapper,
      TaskSessionMapper taskSessionMapper,
      AmAuditSessionMapper amAuditSessionMapper,
      ClientAuditSessionMapper clientAuditSessionMapper,
      BatchDao batchDao,
      IGlobalCache redisCache,
      ResourceDao resourceDao,
      BatchNDADao batchNDADao,
      QuestionGroupDao questionGroupDao,
      AuditLogService auditLogService,
      BatchInstructionHistoriesDao batchInstructionHistoriesDao,
      ObjectMapper objectMapper,
      NotificationService notificationService,
      BatchAccessRequirementDao batchAccessRequirementDao,
      ProjectService projectService,
      BatchSampleService batchSampleService,
      ResourceService resourceService,
      AcademyDao academyDao,
      VaultService vaultService,
      BatchSettingMapper batchSettingMapper,
      BatchRewardRecordDao batchRewardRecordDao,
      RewardTokenInfoDao rewardTokenInfoDao) {
    this.batchMapper = batchMapper;
    this.jobDao = jobDao;
    this.batchDao = batchDao;
    this.redisCache = redisCache;
    this.resourceDao = resourceDao;
    this.batchNDADao = batchNDADao;
    this.taskDao = taskDao;
    this.jobTaskDao = jobTaskDao;
    this.jobMapper = jobMapper;
    this.taskSessionMapper = taskSessionMapper;
    this.batchSettingMapper = batchSettingMapper;
    this.batchSampleDao = batchSampleDao;
    this.taskListDao = taskListDao;
    this.taskService = taskService;
    this.taskQuestionDao = taskQuestionDao;
    this.questionGroupDao = questionGroupDao;
    this.questionAnswerDao = questionAnswerDao;
    this.hybridTaskResourceDao = hybridTaskResourceDao;
    this.amAuditSessionMapper = amAuditSessionMapper;
    this.clientAuditSessionMapper = clientAuditSessionMapper;
    this.auditLogService = auditLogService;
    this.batchInstructionHistoriesDao = batchInstructionHistoriesDao;
    this.objectMapper = objectMapper;
    this.notificationService = notificationService;
    this.batchAccessRequirementDao = batchAccessRequirementDao;
    this.projectService = projectService;
    this.batchSampleService = batchSampleService;
    this.academyDao = academyDao;
    this.resourceService = resourceService;
    this.vaultService = vaultService;
    this.batchRewardRecordDao = batchRewardRecordDao;
    this.rewardTokenInfoDao = rewardTokenInfoDao;
  }

  @Transactional
  public Batch createBatch(Batch batch) {
    var batchDO = batchDao.createBatch(batch);
    var batchId = batch.getId();

    TaskList.TaskListType.values().toList().forEach(t -> {
      var tl = TaskList.builder()
          .batchId(batchId)
          .ownerId(batch.getOwnerId())
          .listType(t)
          .build();
      taskListDao.createTaskList(tl);
    });

    return batchDO;
  }

  @Transactional
  public BatchDetailsDTO createBatchV2(
      Batch batch, List<BatchAccessRequirement> accessRequirements) {
    if (Objects.isNull(batch.getAcademyType())
        && !Batch.TaskType.EXAM.equals(batch.getTaskType())
        && Objects.isNull(batch.getDataType())) {
      throw new AppException("dataType is required for task");
    }

    if (Objects.equals(batch.getTaskType(), Batch.TaskType.TASK)) {
      if (Objects.isNull(batch.getUserType())) {
        throw new AppException("userType is required for task");
      }

      if (Objects.isNull(batch.getDeadline())) {
        throw new AppException("deadline is required for task");
      }

      // if the deadline is 6 months after than current time, throw exception
      if (batch
          .getDeadline()
          .after(Timestamp.from(ZonedDateTime.now(ZoneOffset.UTC).plusMonths(6).toInstant()))) {
        throw new AppException(
            "Time zone: China Standard Time. Please select time within 6 months.");
      }

      if (Objects.equals(batch.getUserType(), Batch.UserType.EXTERNAL_LABELER)) {
        if (Objects.isNull(batch.getDifficulty())) {
          throw new AppException("difficulty is required for task");
        }
      }
    }

    batch.setStatus(Batch.BatchStatus.DRAFT);
    batch.setVersion(2);
    batchDao.createBatch(batch);

    var batchId = batch.getId();
    TaskList.TaskListType.values().toList().forEach(t -> {
      var tl = TaskList.builder()
          .batchId(batchId)
          .ownerId(batch.getOwnerId())
          .listType(t)
          .build();
      taskListDao.createTaskList(tl);
    });

    var isBatchForIndividuals = this.projectService
        .getProjectById(batch.getProjectId())
        .map(p -> Project.ProjectType.INDIVIDUAL.equals(p.getProjectType()))
        .orElse(Boolean.FALSE);
    createLabelSample(batch, isBatchForIndividuals);
    if (Batch.DataType.CONTEXT.getValue().equals(batch.getDataType())
        || Batch.DataType.NO_NEED_TO_UPLOAD.getValue().equals(batch.getDataType())) {
      createTextDataPoint(batch);
    }

    if (CollectionUtils.isNotEmpty(accessRequirements)) {
      accessRequirements.forEach(x -> {
        if (Objects.equals(x.getType(), BatchAccessRequirement.Type.ACADEMY)) {
          Optional<Batch> academy = this.getBatchById(x.getRelationId());
          x.setCertificateId(academy.get().getCertificateId());
        }

        x.setBatchId(batchId);
        batchAccessRequirementDao.create(x);
      });
    }

    BatchSetting batchSetting = Objects.equals(batch.getLabelType(), Batch.BatchLabelType.CONTEXT)
            || Objects.equals(batch.getLabelType(), Batch.BatchLabelType.NO_NEED_TO_UPLOAD)
        ? BatchSetting.builder()
            .distributeType(BatchSetting.DistributeType.SINGLE)
            .taskPaymentType(BatchSetting.TaskPaymentType.REWARD_POOL)
            .batchId(batchId)
            .build()
        : BatchSetting.builder()
            .taskPaymentType(BatchSetting.TaskPaymentType.REWARD_POOL)
            .distributeType(BatchSetting.DistributeType.RAW)
            .batchId(batchId)
            .build();

    this.batchDao.saveBatchSetting(batchSetting);

    return this.getBatchDetails(batchId);
  }

  public BatchSample getLabelSample(Batch batch) {
    return batchSampleDao.getBatchLabelSamplesByBatch(batch);
  }

  @Transactional
  public BatchSample createLabelSample(Batch batch, Boolean isBatchForIndividuals) {
    var labelingTaskList = taskListDao
        .listTaskListsByBatchIdAndType(batch.getId(), TaskList.TaskListType.LABEL)
        .filter(tl -> !tl.getDeleted())
        .toList()
        .first();

    var file = new MockMultipartFile(
        Constants.DEFAULT_LABELING_RESOURCE_NAME,
        Constants.DEFAULT_LABELING_RESOURCE_NAME,
        ResourceService.genDefaultContentTypeFromFileType(batch.resourceType()),
        "".getBytes());
    var resource =
        resourceService.createResourceFromFile(batch.getOwnerId(), file, Resource.Visibility.TASK);

    var samples = List.of(BatchSample.builder()
        .batchId(batch.getId())
        .resourceId(resource.getId())
        .ownerId(batch.getOwnerId())
        .build());

    var isHybridBatch = batch.getLabelType() == Batch.BatchLabelType.HYBRID;
    List<BatchSample> batchSamples;
    if (isHybridBatch && Objects.nonNull(isBatchForIndividuals) && !isBatchForIndividuals) {
      batchSamples = batchSampleService.createHybridBatchSamples(samples, labelingTaskList.getId());
    } else {
      batchSamples = batchSampleService.createBatchSamples(samples, labelingTaskList.getId());
    }
    return CollectionUtils.isEmpty(batchSamples) ? null : batchSamples.get(0);
  }

  public Task createTextDataPoint(Batch batch) {
    var taskLists = taskListDao
        .listTaskListsByBatchId(batch.getId())
        .filter(tl -> !tl.getDeleted())
        .toList();

    var labelingTaskList = taskLists
        .filter(taskList -> TaskList.TaskListType.LABEL.equals(taskList.getListType()))
        .findFirst()
        .orElseThrow(() -> ControllerUtils.notFound("label task list not found"));

    var annotationTaskList = taskLists
        .filter(taskList -> TaskList.TaskListType.ANNOTATION.equals(taskList.getListType()))
        .findFirst()
        .orElseThrow(() -> ControllerUtils.notFound("annotation task list not found"));

    var resource =
        resourceService.createResourceFromText("", batch.getOwnerId(), Resource.Visibility.TASK);

    var firstLabelTask =
        taskService.listTasksByTaskListId(batch, labelingTaskList.getId()).firstOrNull();
    var fromTaskId = Objects.isNull(firstLabelTask) ? null : firstLabelTask.getId();
    var task = Task.builder()
        .taskListId(annotationTaskList.getId())
        .resourceId(resource.getId())
        .ownerId(batch.getOwnerId())
        .taskType(Task.TaskType.TASK)
        .status(Task.TaskStatus.READY)
        .originId(fromTaskId)
        .sort(0)
        .build();

    return taskDao.createTask(task);
  }

  @Transactional
  public void deleteBatchById(
      Long id, AuditLogInterceptor.RequestUserAgent ua, UserDetails currentUserDetail) {
    batchDao.deleteBatchById(id);
    taskListDao.deleteTaskListByBatchId(id);
    auditLogService.log(
        AuditLog.AuditLogOperation.DELETE_RESOURCE, "batch ${id}", ua, currentUserDetail);
  }

  public Optional<Batch> getBatchById(Long id) {
    return batchDao.getBatchById(id);
  }

  public List<RewardTokenInfo> getRewardTokenInfoList() {
    return rewardTokenInfoDao.findAllActive();
  }

  public List<Batch> getBatchByIds(List<Long> ids) {
    return batchDao.getBatchByIds(ids);
  }

  public BatchDetailsDTO getBatchDetails(Long id) {
    var batch = batchDao.getBatchById(id).orElseThrow(() -> new AppException("batch not found"));
    var batchSetting = batchDao.getBatchSettingById(id);
    List<BatchAccessRequirement> batchAccessRequirements =
        this.getBatchAccessRequirements(batch.getId());
    var samples = batchSampleService.listBatchSamplesByBatch(batch);

    var rewardRecords = batchRewardRecordDao.listByBatchId(id);
    List<RewardTokenDTO> records = new ArrayList<>();
    if (!rewardRecords.isEmpty()) {
      var tokensMap = rewardTokenInfoDao
          .findByIds(rewardRecords.mapToList(BatchRewardRecord::getTokenInfoId))
          .toMap(RewardTokenInfo::getId, Function.identity());
      records.addAll(rewardRecords.stream()
          .map(r -> RewardTokenDTO.builder()
              .recordId(r.getId())
              .tokenInfoId(r.getTokenInfoId())
              .amount(r.getAmount())
              .rewardTokenType(r.getRewardTokenType())
              .tokenName(tokensMap.get(r.getTokenInfoId()).getTokenName())
              .tokenIconResourceId(tokensMap.get(r.getTokenInfoId()).getResourceId())
              .decimals(tokensMap.get(r.getTokenInfoId()).getTokenDecimal())
              .tgeAlready(tokensMap.get(r.getTokenInfoId()).getTgeAlready())
              .build())
          .toList());
    }

    return BatchDetailsDTO.builder()
        .batch(batch)
        .samples(samples)
        .batchSetting(batchSetting)
        .accessRequirements(batchAccessRequirements)
        .certificate(
            Objects.isNull(batch.getCertificateId())
                ? null
                : this.vaultService.getCertificateById(batch.getCertificateId()))
        .academy(
            Batch.TaskType.ACADEMY.equals(batch.getTaskType())
                ? this.academyDao.getAcademyByBatchId(id)
                : null)
        .rewards(records)
        .build();
  }

  public List<BatchAccessRequirement> getBatchAccessRequirements(Long batchId) {
    return batchAccessRequirementDao.getBatchAccessRequirementsByBatchId(batchId);
  }

  public IPage<Batch> getUserBatches(Long curId, Integer first, Integer second) {
    return batchDao.getUserBatches(curId, first, second);
  }

  public IPage<Batch> getUserBatchesByProject(
      Long curId, Integer page, Integer size, Long projectId, Batch.TaskType type) {
    return batchDao.getUserBatchesByProject(curId, page, size, projectId, type);
  }

  public Batch getBatchByCertificateId(Long certificateId) {
    return batchDao.getByCertificateId(certificateId);
  }

  public List<BatchDetailsDTO> getBatchDetailsByProjectId(Long id) {
    var batches = batchDao.getBatchesByProjectId(id);
    return batches
        .map(b -> BatchDetailsDTO.builder()
            .batch(b)
            .samples(batchSampleDao.listBatchSamplesByBatch(b))
            .build())
        .toList();
  }

  public Long countLaunchedBatchByProjectId(Long id) {
    return batchDao.countLaunchedBatchByProjectId(id);
  }

  public void resetBatchStep(Long id, Batch.BatchStatus status) {
    batchDao.resetBatchStep(id, status);
  }

  @Transactional
  public void updateBatchDataV2(Long id, UpdateBatchDTO data, Batch batchDB) {
    var mapper = new ObjectMapper();
    var update = new UpdateWrapper<Batch>()
        .lambda()
        .eq(Batch::getId, id)
        .set(Batch::getUpdatedAt, DateUtils.now());
    var settingUpdate = new UpdateWrapper<BatchSetting>()
        .lambda()
        .eq(BatchSetting::getBatchId, id)
        .set(BatchSetting::getUpdatedAt, DateUtils.now());
    if (data.getReviewerRequired() != null
        && Boolean.FALSE.equals(data.getReviewerRequired())
        && !batchDB.getReviewerRequired().equals(data.getReviewerRequired())) {
      settingUpdate.set(BatchSetting::getCutoffTime, null);
    }
    List<BatchInstructionHistories> batchInstructionHistories = this.diffInstruction(data, batchDB);

    applyBasicBatchUpdates(data, update, settingUpdate, mapper);
    applyAnnotatingSettingsUpdates(data, update, settingUpdate);
    applyReviewingSettingsUpdates(data, update);
    applyBonusSettingsUpdates(data, update);
    applyExamSettingsUpdates(data, update);
    applyHoneypotUpdates(data, settingUpdate);
    applyOtherUpdates(data, update, settingUpdate);

    if (Objects.nonNull(data.getDeadline()) && !data.getDeadline().equals(batchDB.getDeadline())) {
      this.jobDao.updateDeadline(id, data.getDeadline());
    }
    if (Objects.nonNull(data.getName())) {
      this.jobDao.updateName(id, data.getName());
    }

    //    if (CollectionUtils.isEmpty(data.getRewards())) { // shoule check before launch
    //      throw new AppException("Rewards cannot be empty");
    //    }
    //
    //    if (data.getRewards().size() > 2) {
    //      throw new AppException("A task can have at most two types of token rewards.");
    //    }

    if (null != data.getRewards()) {
      Map<Long, BatchRewardRecord> existingRecordsMap =
          batchRewardRecordDao.listByBatchId(id).stream()
              .collect(Collectors.toMap(BatchRewardRecord::getId, Function.identity()));

      List<BatchRewardRecord> recordsToUpdate = new ArrayList<>();
      List<BatchRewardRecord> recordsToInsert = new ArrayList<>();

      for (RewardTokenDTO newReward : data.getRewards()) {
        var existingRecord = existingRecordsMap.get(newReward.getRecordId());
        if (existingRecord != null) {
          if (afterLaunchBatchStatusList.contains(batchDB.getStatus())
              && newReward.getAmount().compareTo(existingRecord.getAmount()) < 0) {
            throw new AppException("Reward amount can only be increased for a launched task.");
          }
          existingRecord.setAmount(newReward.getAmount());
          recordsToUpdate.add(existingRecord);
          existingRecordsMap.remove(
              newReward.getRecordId()); // Remove from map so we know it's been handled
        } else {
          if (afterLaunchBatchStatusList.contains(batchDB.getStatus())) {
            throw new AppException("Cannot add new reward types for a launched task.");
          }
          recordsToInsert.add(BatchRewardRecord.builder()
              .batchId(id)
              .tokenInfoId(newReward.getTokenInfoId())
              .rewardTokenType(newReward.getRewardTokenType())
              .amount(newReward.getAmount())
              .tokenDecimal(newReward.getDecimals())
              .deleted(false)
              .build());
        }
      }
      // Any rewards left in the map are to be deleted
      List<Long> recordIdsToDelete =
          existingRecordsMap.values().stream().map(BatchRewardRecord::getId).collect(toList());
      if (!recordIdsToDelete.isEmpty()
          && afterLaunchBatchStatusList.contains(batchDB.getStatus())) {
        throw new AppException("Cannot remove token types from a launched task.");
      }
      batchRewardRecordDao.updateAll(recordsToUpdate);
      batchRewardRecordDao.saveAll(recordsToInsert);
      batchRewardRecordDao.deleteByIds(recordIdsToDelete);
    }

    if (data.getLabelType() != null
        && !Objects.equals(data.getLabelType(), batchDB.getLabelType())) {
      // delete all relation task & batch sample
      var taskLists = taskListDao.listTaskListsByBatchId(id);
      taskLists.forEach((taskList) -> taskService.deleteTaskByTaskListId(taskList.getId()));
      batchSampleDao.deleteBatchSampleByBatchId(id);

      // update batch status to draft
      update.set(Batch::getStatus, Batch.BatchStatus.DRAFT);
    }

    if (!batchInstructionHistories.isEmpty()) {
      batchInstructionHistories.forEach(x -> this.batchInstructionHistoriesDao.create(x));
      this.jobDao
          .listExistingJobsByBatchId(batchDB.getId())
          .filter(x -> Objects.nonNull(x.getNodeId()))
          .map(Job::getNodeId)
          .distinct()
          .forEach(x -> this.notificationService.noticeBatchInstruction(batchDB, x));
    }
    this.batchMapper.update(null, update);
    this.batchSettingMapper.update(null, settingUpdate);

    if (Objects.nonNull(data.getAccessRequirements())) {
      this.batchAccessRequirementDao.deleteBatchAccessRequirementByBatchId(batchDB.getId());
      data.getAccessRequirements().forEach(x -> {
        if (Objects.equals(x.getType(), BatchAccessRequirement.Type.ACADEMY)) {
          Optional<Batch> academy = this.getBatchById(x.getRelationId());
          x.setCertificateId(academy.get().getCertificateId());
        }
        x.setBatchId(id);
        this.batchAccessRequirementDao.create(x);
      });
    }

    BatchDetailsDTO batchDetails = this.getBatchDetails(id);
    if (Objects.nonNull(data.getCertificateId())) {
      Certificate certificate = this.vaultService.getCertificateById(data.getCertificateId());
      if (Objects.isNull(certificate)) {
        throw new AppException("certificate not found");
      }

      certificate.setCategory(batchDetails.getBatch().getAcademyType());
      certificate.setCertificateType(batchDetails.getBatch().getCourseType());
      certificate.setName(data.getCertificateName());
      certificate.setExp(data.getExperiencePoint());

      var knowledgeList = Optional.ofNullable(data.getKnowledge())
          .or(() -> Optional.ofNullable(batchDetails.getBatch().getKnowledge()))
          .map((k) -> {
            var list = data.getKnowledge().split(",").toList();
            if (list.isEmpty() && !k.isEmpty()) {
              return List.of(k);
            } else {
              return list;
            }
          });
      certificate.setKnowledgeList(knowledgeList.orElse(certificate.getKnowledgeList()));

      this.vaultService.updateCertificate(certificate);

      List<UserCertificate> userCertificates =
          this.vaultService.getUserCertificatesByCertificateId(data.getCertificateId());
      for (var userCert : userCertificates) {
        // Check whether update affect points. Upgrade parent certificate Lv give points
        if (!userCert.getExp().equals(certificate.getExp())) {
          throw new AppException("user certificate found, cannot update certificate exp");
        }
        if (!userCert.getCategory().equals(certificate.getCategory())) {
          throw new AppException(
              "user certificate found, cannot update certificate to ${certificate.getCategory()}");
        }
        if (!userCert.getCertificateType().equals(certificate.getCertificateType())) {
          throw new AppException(
              "user certificate found, cannot update certificate to ${certificate.getCertificateType()}");
        }

        userCert.setName(certificate.getName());
        userCert.setKnowledgeList(certificate.getKnowledgeList());
        this.vaultService.updateUserCertificate(userCert);
      }
    }

    if (!Objects.equals(batchDetails.getBatch().getTaskType(), Batch.TaskType.ACADEMY)
        && Objects.isNull(batchDetails.getBatch().getAcademyType())) {
      notificationService.noticeAccountManagerBatchCalibration(
          batchDB.getOwnerId(), batchDB.getProjectId(), batchDB.getId());
    }
  }

  private void applyBasicBatchUpdates(
      UpdateBatchDTO data,
      LambdaUpdateWrapper<Batch> update,
      LambdaUpdateWrapper<BatchSetting> settingUpdate,
      ObjectMapper mapper) {
    data.getStakingRequest()
        .asOpt()
        .ifPresent(staking -> settingUpdate.set(BatchSetting::getStakingRequest, staking));
    data.getName().asOpt().ifPresent(name -> update.set(Batch::getName, name));
    data.getSummary().asOpt().ifPresent(summary -> update.set(Batch::getSummary, summary));
    data.getSummaryForAnnotator()
        .asOpt()
        .ifPresent(
            summaryForAnnotator -> update.set(Batch::getSummaryForAnnotator, summaryForAnnotator));
    data.getSummaryForReviewer()
        .asOpt()
        .ifPresent(
            summaryForReviewer -> update.set(Batch::getSummaryForReviewer, summaryForReviewer));
    data.getDescription()
        .asOpt()
        .ifPresent(description -> update.set(Batch::getDescription, description));
    data.getExtraInfo().asOpt().ifPresent(extraInfo -> update.set(Batch::getExtraInfo, extraInfo));
    data.getDeadline().asOpt().ifPresent(deadline -> update.set(Batch::getDeadline, deadline));
    data.getTaskType().asOpt().ifPresent(taskType -> update.set(Batch::getTaskType, taskType));
    data.getUserType().asOpt().ifPresent(userType -> update.set(Batch::getUserType, userType));
    data.getExamType().asOpt().ifPresent(examType -> update.set(Batch::getExamType, examType));
    data.getReviewerRequired()
        .asOpt()
        .ifPresent(reviewerRequired -> update.set(Batch::getReviewerRequired, reviewerRequired));
    if (data.getReviewerRequired().asOpt().isPresent() && !data.getReviewerRequired()) {
      update.set(Batch::getBonusMinimumSubmissions, 0);
      update.set(Batch::getBonusRequiredAccuracy, 0);
      update.set(Batch::getBonusPercentage, 0);
      settingUpdate.setJson(BatchSetting::getHoneyPotReviewBatches, new ArrayList<>());
    }
    data.getDifficulty()
        .asOpt()
        .ifPresent(difficulty -> update.set(Batch::getDifficulty, difficulty));
    data.getSpecialRequirement()
        .asOpt()
        .ifPresent(
            specialRequirement -> update.set(Batch::getSpecialRequirement, specialRequirement));
    data.getGender().asOpt().ifPresent(gender -> update.set(Batch::getGender, gender));
    data.getAccent().asOpt().ifPresent(accent -> update.set(Batch::getAccent, accent));
    data.getRequiredRegions()
        .asOpt()
        .ifPresent(
            regions -> update.set(Batch::getRequiredRegions, mapper.writeValueAsString(regions)));
    data.getRequiredLanguages()
        .asOpt()
        .ifPresent(languages ->
            update.set(Batch::getRequiredLanguages, mapper.writeValueAsString(languages)));
    data.getAgeMin().asOpt().ifPresent(ageMin -> update.set(Batch::getAgeMin, ageMin));
    data.getAgeMax().asOpt().ifPresent(ageMax -> update.set(Batch::getAgeMax, ageMax));
  }

  private void applyAnnotatingSettingsUpdates(
      UpdateBatchDTO data,
      LambdaUpdateWrapper<Batch> update,
      LambdaUpdateWrapper<BatchSetting> settingUpdate) {
    data.getRequiredAccuracy().asOpt().ifPresent(v -> update.set(Batch::getRequiredAccuracy, v));
    data.getAnnotatingPrice()
        .asOpt()
        .ifPresent(annotatingPrice -> update.set(Batch::getAnnotatingPrice, annotatingPrice));
    data.getAnnotatingSubmitRequired()
        .asOpt()
        .ifPresent(
            submitRequired -> update.set(Batch::getAnnotatingSubmitRequired, submitRequired));
    data.getAnnotatingMinDatapointSecond()
        .asOpt()
        .ifPresent(minDatapointSecond -> update.set(
            Batch::getAnnotatingMinDatapointSecond,
            Objects.equals(minDatapointSecond, -1) ? null : minDatapointSecond));
    data.getAnnotatingTimesAnnotationPerDatapoint()
        .asOpt()
        .ifPresent(annotatingTimesAnnotationPerDatapoint -> settingUpdate.set(
            BatchSetting::getAnnotatingTimesAnnotationPerDatapoint,
            annotatingTimesAnnotationPerDatapoint));
    data.getAnnotatingTimesPerDatapointPerUser()
        .asOpt()
        .ifPresent(annotatingTimesPerDatapointPerUser -> settingUpdate.set(
            BatchSetting::getAnnotatingTimesPerDatapointPerUser,
            annotatingTimesPerDatapointPerUser));
  }

  private void applyReviewingSettingsUpdates(
      UpdateBatchDTO data, LambdaUpdateWrapper<Batch> update) {
    data.getRequiredReviewAccuracy()
        .asOpt()
        .ifPresent(v -> update.set(Batch::getRequiredReviewAccuracy, v));
    data.getReviewingPrice()
        .asOpt()
        .ifPresent(reviewingPrice -> update.set(Batch::getReviewingPrice, reviewingPrice));
    data.getReviewingRequiredDatapoint()
        .asOpt()
        .ifPresent(reviewingRequiredDatapoint ->
            update.set(Batch::getReviewingRequiredDatapoint, reviewingRequiredDatapoint));
    data.getReviewingTimesReviewPerDatapoint()
        .asOpt()
        .ifPresent(reviewingTimesReviewPerDatapoint -> update.set(
            Batch::getReviewingTimesReviewPerDatapoint, reviewingTimesReviewPerDatapoint));
    data.getReviewingMinDatapointSecond()
        .asOpt()
        .ifPresent(reviewingMinDatapointSecond -> update.set(
            Batch::getReviewingMinDatapointSecond,
            Objects.equals(reviewingMinDatapointSecond, -1) ? null : reviewingMinDatapointSecond));
  }

  private void applyBonusSettingsUpdates(UpdateBatchDTO data, LambdaUpdateWrapper<Batch> update) {
    data.getBonusRequiredAccuracy()
        .asOpt()
        .ifPresent(bonusRequiredAccuracy ->
            update.set(Batch::getBonusRequiredAccuracy, bonusRequiredAccuracy));
    data.getBonusPercentage()
        .asOpt()
        .ifPresent(bonusPercentage -> update.set(Batch::getBonusPercentage, bonusPercentage));
    data.getBonusMinimumSubmissions()
        .asOpt()
        .ifPresent(bonusMinimumSubmissions ->
            update.set(Batch::getBonusMinimumSubmissions, bonusMinimumSubmissions));
  }

  private void applyExamSettingsUpdates(UpdateBatchDTO data, LambdaUpdateWrapper<Batch> update) {
    if (Objects.nonNull(data.getExamNumeratorForReviewing())
        && Objects.nonNull(data.getExamNumerator())
        && data.getExamNumeratorForReviewing() < data.getExamNumerator()) {
      throw new AppException(
          "examNumeratorForReviewing must be greater than or equal to examNumerator");
    }

    // Exam setting
    data.getShowAnswer()
        .asOpt()
        .ifPresent(showAnswer -> update.set(Batch::getShowAnswer, showAnswer));
    data.getExamNumerator()
        .asOpt()
        .ifPresent(examNumerator -> update.set(Batch::getExamNumerator, examNumerator));
    data.getExamNumeratorForReviewing()
        .asOpt()
        .ifPresent(examNumeratorForReviewing ->
            update.set(Batch::getExamNumeratorForReviewing, examNumeratorForReviewing));
    data.getExamNenominator()
        .asOpt()
        .ifPresent(examNenominator -> update.set(
            Batch::getExamNenominator,
            Objects.equals(examNenominator, -1) ? null : examNenominator));
    data.getEstExamTime()
        .asOpt()
        .ifPresent(estExamTime -> update.set(Batch::getEstExamTime, estExamTime));
    // data.getPassingThreshold().asOpt()
    // .map(passingThreshold -> update.set(Batch::getPassingThreshold, passingThreshold));

    data.getEstEarnings()
        .asOpt()
        .ifPresent(estEarnings -> update.set(Batch::getEstEarnings, estEarnings));
  }

  private void applyHoneypotUpdates(
      UpdateBatchDTO data, LambdaUpdateWrapper<BatchSetting> settingUpdate) {
    data.getHoneyPotBatchId().asOpt().ifPresent(honeyPotBatchId -> {
      if (StringUtils.isNotEmpty(honeyPotBatchId)) {
        var batchList = Splitter.on(',').split(honeyPotBatchId).mapToList(batchId -> {
          var batchSetting = batchDao.getBatchSettingById(Long.valueOf(batchId));
          if (!BatchSetting.HpTargetUserType.ALL.equals(batchSetting.getHpTargetUserType())
              && !BatchSetting.HpTargetUserType.LABELER.equals(
                  batchSetting.getHpTargetUserType())) {
            throw new AppException(
                "Invalid honeypot database, target users must be annotator or all.");
          }
          return HoneyPotBatch.builder().batchId(Long.valueOf(batchId)).build();
        });
        data.getHoneyPotPrimaryBatchId().asOpt().ifPresent(primaries -> {
          var primaryIdList = Splitter.on(',').split(primaries).toList();
          batchList.forEach(b -> {
            if (primaryIdList.contains(b.getBatchId().toString())) {
              b.setIsPrimary(true);
            }
          });
        });
        settingUpdate.setJson(BatchSetting::getHoneyPotBatches, batchList);
      } else {
        settingUpdate.setJson(BatchSetting::getHoneyPotBatches, new ArrayList<>());
      }
    });

    data.getHoneyPotNumberPerSubmit()
        .asOpt()
        .ifPresent(perSub -> settingUpdate.set(BatchSetting::getHoneyPotNumberPerSubmit, perSub));
    data.getHoneyPotWeight()
        .asOpt()
        .ifPresent(hpWeight -> settingUpdate.set(BatchSetting::getHoneyPotWeight, hpWeight));
    data.getHoneyPotAccuracyRequired()
        .asOpt()
        .ifPresent(
            hpAcReq -> settingUpdate.set(BatchSetting::getHoneyPotAccuracyRequired, hpAcReq));
    data.getHoneyPotBatchIdReview().asOpt().ifPresent(hpBatchIdReview -> {
      if (StringUtils.isNotEmpty(hpBatchIdReview)) {
        // honeyPotPrimaryBatchIdReview
        var batchList = Splitter.on(',').split(hpBatchIdReview).mapToList(batchId -> {
          var batchSetting = batchDao.getBatchSettingById(Long.valueOf(batchId));
          if (!BatchSetting.HpTargetUserType.ALL.equals(batchSetting.getHpTargetUserType())
              && !BatchSetting.HpTargetUserType.REVIEWER.equals(
                  batchSetting.getHpTargetUserType())) {
            throw new AppException(
                "Invalid honeypot database, target users must be reviewer or all.");
          }
          return HoneyPotBatch.builder().batchId(Long.valueOf(batchId)).build();
        });
        data.getHoneyPotPrimaryBatchIdReview().asOpt().ifPresent(primaries -> {
          var primaryIdList = Splitter.on(',').split(primaries).toList();
          batchList.forEach(b -> {
            if (primaryIdList.contains(b.getBatchId().toString())) {
              b.setIsPrimary(true);
            }
          });
        });
        settingUpdate.setJson(BatchSetting::getHoneyPotReviewBatches, batchList);
      } else {
        settingUpdate.setJson(BatchSetting::getHoneyPotReviewBatches, new ArrayList<>());
      }
    });
    data.getHoneyPotNumberPerSubmitReview()
        .asOpt()
        .ifPresent(hpNumPerSubReview ->
            settingUpdate.set(BatchSetting::getHoneyPotNumberPerSubmitReview, hpNumPerSubReview));
    data.getHoneyPotWeightReview()
        .asOpt()
        .ifPresent(hpWeightReview ->
            settingUpdate.set(BatchSetting::getHoneyPotWeightReview, hpWeightReview));
    data.getHoneyPotAccuracyRequiredReview()
        .asOpt()
        .ifPresent(hpAcReqReview ->
            settingUpdate.set(BatchSetting::getHoneyPotAccuracyRequiredReview, hpAcReqReview));
    data.getHoneypotQuestionLocation()
        .asOpt()
        .ifPresent(honeypotQuestionLocation ->
            settingUpdate.set(BatchSetting::getHoneypotQuestionLocation, honeypotQuestionLocation));
    data.getHoneypotReviewQuestionLocation()
        .asOpt()
        .ifPresent(honeypotReviewQuestionLocation -> settingUpdate.set(
            BatchSetting::getHoneypotReviewQuestionLocation, honeypotReviewQuestionLocation));
    data.getHoneyPotSettingForLabeler()
        .asOpt()
        .ifPresent(honeyPotDpBoundForReviewer -> settingUpdate.set(
            BatchSetting::getHoneyPotSettingForLabeler, honeyPotDpBoundForReviewer));
    data.getHoneyPotSettingForReviewer()
        .asOpt()
        .ifPresent(honeyPotDpBoundForLabeler -> settingUpdate.set(
            BatchSetting::getHoneyPotSettingForReviewer, honeyPotDpBoundForLabeler));
  }

  private void applyOtherUpdates(
      UpdateBatchDTO data,
      LambdaUpdateWrapper<Batch> update,
      LambdaUpdateWrapper<BatchSetting> settingUpdate) {
    data.getAllowCopy()
        .asOpt()
        .ifPresent(allowCopy -> settingUpdate.set(BatchSetting::getAllowCopy, allowCopy));
    data.getWorkloadMaxSybilJobUserPercent()
        .asOpt()
        .ifPresent(workloadMaxSybilJobUserPercent -> settingUpdate.set(
            BatchSetting::getWorkloadMaxSybilJobUserPercent, workloadMaxSybilJobUserPercent));
    data.getWorkloadMaxSybilJobReviewerPercent()
        .asOpt()
        .ifPresent(workloadMaxSybilJobReviewerPercent -> settingUpdate.set(
            BatchSetting::getWorkloadMaxSybilJobReviewerPercent,
            workloadMaxSybilJobReviewerPercent));
    data.getWorkloadMaxPerJobUser()
        .asOpt()
        .ifPresent(workloadMaxPerJobUser ->
            settingUpdate.set(BatchSetting::getWorkloadMaxPerJobUser, workloadMaxPerJobUser));
    data.getWorkloadMaxPerJobUserDay()
        .asOpt()
        .ifPresent(workloadMaxPerJobUserDay ->
            settingUpdate.set(BatchSetting::getWorkloadMaxPerJobUserDay, workloadMaxPerJobUserDay));
    data.getWorkloadMaxPerJobUserHour()
        .asOpt()
        .ifPresent(workloadMaxPerJobUserHour -> settingUpdate.set(
            BatchSetting::getWorkloadMaxPerJobUserHour, workloadMaxPerJobUserHour));
    data.getWorkloadMaxPerJobReviewer()
        .asOpt()
        .ifPresent(workloadMaxPerJobReviewer -> settingUpdate.set(
            BatchSetting::getWorkloadMaxPerJobReviewer, workloadMaxPerJobReviewer));
    data.getWorkloadMaxPerJobReviewerDay()
        .asOpt()
        .ifPresent(workloadMaxPerJobReviewerDay -> settingUpdate.set(
            BatchSetting::getWorkloadMaxPerJobReviewerDay, workloadMaxPerJobReviewerDay));
    data.getWorkloadMaxPerJobReviewerHour()
        .asOpt()
        .ifPresent(workloadMaxPerJobReviewerHour -> settingUpdate.set(
            BatchSetting::getWorkloadMaxPerJobReviewerHour, workloadMaxPerJobReviewerHour));
    data.getRequiredAchievementSymbol()
        .asOpt()
        .ifPresent(achievementSymbol ->
            update.set(Batch::getRequiredAchievementSymbol, achievementSymbol));

    data.getStatus().asOpt().ifPresent(status -> update.set(Batch::getStatus, status));
    data.getLabelType().asOpt().ifPresent(labelType -> update.set(Batch::getLabelType, labelType));
    data.getSpotAudit().asOpt().ifPresent(v -> update.set(Batch::getSpotAudit, v));
    data.getLabelingRepeat().asOpt().ifPresent(v -> update.set(Batch::getLabelingRepeat, v));
    data.getReviewRepeat().asOpt().ifPresent(v -> update.set(Batch::getReviewRepeat, v));

    data.getKnowledge().asOpt().ifPresent(knowledge -> update.set(Batch::getKnowledge, knowledge));
    data.getCourseCover()
        .asOpt()
        .ifPresent(courseCover -> update.set(Batch::getCourseCover, courseCover));
    data.getCourseType()
        .asOpt()
        .ifPresent(courseType -> update.set(Batch::getCourseType, courseType));
    data.getCourseDifficulty()
        .asOpt()
        .ifPresent(courseDifficulty -> update.set(Batch::getCourseDifficulty, courseDifficulty));
    data.getAcademyType()
        .asOpt()
        .ifPresent(academyType -> update.set(Batch::getAcademyType, academyType));
    data.getTaskPaymentType()
        .asOpt()
        .ifPresent(taskPaymentType ->
            settingUpdate.set(BatchSetting::getTaskPaymentType, taskPaymentType));
    data.getCutoffTime()
        .asOpt()
        .ifPresent(
            taskCutoffTime -> settingUpdate.set(BatchSetting::getCutoffTime, taskCutoffTime));
    data.getAnnotationMethod()
        .asOpt()
        .ifPresent(annotationMethod ->
            settingUpdate.set(BatchSetting::getAnnotationMethod, annotationMethod));
    data.getRequiredUserLevel()
        .asOpt()
        .ifPresent(requiredUserLevel ->
            settingUpdate.set(BatchSetting::getRequiredUserLevel, requiredUserLevel));
  }

  @Transactional
  public void updateBatchData(Long id, UpdateBatchDTO data, Batch batchDB) {
    var update = new UpdateWrapper<Batch>()
        .lambda()
        .eq(Batch::getId, id)
        .set(Batch::getUpdatedAt, DateUtils.now());

    List<BatchInstructionHistories> batchInstructionHistories = this.diffInstruction(data, batchDB);

    data.getSummary().asOpt().ifPresent(summary -> update.set(Batch::getSummary, summary));
    data.getSummaryForAnnotator()
        .asOpt()
        .ifPresent(
            summaryForAnnotator -> update.set(Batch::getSummaryForAnnotator, summaryForAnnotator));
    data.getSummaryForReviewer()
        .asOpt()
        .ifPresent(
            summaryForReviewer -> update.set(Batch::getSummaryForReviewer, summaryForReviewer));
    data.getDescription()
        .asOpt()
        .ifPresent(description -> update.set(Batch::getDescription, description));
    data.getExtraInfo().asOpt().ifPresent(extraInfo -> update.set(Batch::getExtraInfo, extraInfo));
    data.getDeadline().asOpt().ifPresent(deadline -> update.set(Batch::getDeadline, deadline));
    data.getLabelType().asOpt().ifPresent(labelType -> update.set(Batch::getLabelType, labelType));

    var mapper = new ObjectMapper();
    data.getRequiredRegions()
        .asOpt()
        .ifPresent(
            regions -> update.set(Batch::getRequiredRegions, mapper.writeValueAsString(regions)));
    data.getRequiredLanguages()
        .asOpt()
        .ifPresent(languages ->
            update.set(Batch::getRequiredLanguages, mapper.writeValueAsString(languages)));
    data.getStatus().asOpt().ifPresent(status -> update.set(Batch::getStatus, status));
    data.getRequiredAccuracy().asOpt().ifPresent(v -> update.set(Batch::getRequiredAccuracy, v));
    data.getRequiredReviewAccuracy()
        .asOpt()
        .ifPresent(v -> update.set(Batch::getRequiredReviewAccuracy, v));
    data.getSpotAudit().asOpt().ifPresent(v -> update.set(Batch::getSpotAudit, v));
    data.getLabelingRepeat().asOpt().ifPresent(v -> update.set(Batch::getLabelingRepeat, v));
    data.getReviewRepeat().asOpt().ifPresent(v -> update.set(Batch::getReviewRepeat, v));

    if (data.getLabelType() != null
        && !Objects.equals(data.getLabelType(), batchDB.getLabelType())) {
      // delete all relation task & batch sample
      var taskLists = taskListDao.listTaskListsByBatchId(id);
      taskLists.forEach((taskList) -> taskService.deleteTaskByTaskListId(taskList.getId()));
      batchSampleDao.deleteBatchSampleByBatchId(id);

      // update batch status to draft
      update.set(Batch::getStatus, Batch.BatchStatus.DRAFT);
    }

    if (!batchInstructionHistories.isEmpty()) {
      batchInstructionHistories.forEach(x -> this.batchInstructionHistoriesDao.create(x));
      this.jobDao
          .listExistingJobsByBatchId(batchDB.getId())
          .filter(x -> Objects.nonNull(x.getNodeId()))
          .map(Job::getNodeId)
          .distinct()
          .forEach(x -> this.notificationService.noticeBatchInstruction(batchDB, x));
    }
    this.batchMapper.update(null, update);
  }

  public List<BatchInstructionHistories> getBatchInstructionHistories(
      Long batchId, BatchInstructionHistories.BatchInstructionType type, Integer limit) {
    return this.batchInstructionHistoriesDao.getBatchInstructionHistoriesByBatchIdAndType(
        batchId, type, limit);
  }

  private List<BatchInstructionHistories> diffInstruction(UpdateBatchDTO data, Batch batchDB) {
    if (Objects.equals(batchDB.getStatus(), Batch.BatchStatus.DRAFT)) {
      return Collections.EMPTY_LIST;
    }

    List<BatchInstructionHistories> batchInstructionHistories = new ArrayList<>(3);

    if (Objects.nonNull(data.getExtraInfo())
        && !Objects.equals(data.getExtraInfo(), batchDB.getExtraInfo())) {
      batchInstructionHistories.add(BatchInstructionHistories.builder()
          .batchId(batchDB.getId())
          .ownerId(batchDB.getOwnerId())
          .projectId(batchDB.getProjectId())
          .content(batchDB.getExtraInfo())
          .type(BatchInstructionHistories.BatchInstructionType.EXTRA_INFO)
          .build());
    }

    if (Objects.nonNull(data.getDescription())
        && !Objects.equals(data.getDescription(), batchDB.getDescription())) {
      batchInstructionHistories.add(BatchInstructionHistories.builder()
          .batchId(batchDB.getId())
          .ownerId(batchDB.getOwnerId())
          .projectId(batchDB.getProjectId())
          .content(batchDB.getDescription())
          .type(BatchInstructionHistories.BatchInstructionType.DESCRIPTION)
          .build());
    }

    if (Objects.nonNull(data.getSummary())
        && !Objects.equals(data.getSummary(), batchDB.getSummary())) {
      batchInstructionHistories.add(BatchInstructionHistories.builder()
          .batchId(batchDB.getId())
          .ownerId(batchDB.getOwnerId())
          .projectId(batchDB.getProjectId())
          .content(Objects.isNull(batchDB.getSummary()) ? "" : batchDB.getSummary())
          .type(BatchInstructionHistories.BatchInstructionType.SUMMARY)
          .build());
    }

    if (Objects.nonNull(data.getSummaryForAnnotator())
        && !Objects.equals(data.getSummaryForAnnotator(), batchDB.getSummaryForAnnotator())) {
      batchInstructionHistories.add(BatchInstructionHistories.builder()
          .batchId(batchDB.getId())
          .ownerId(batchDB.getOwnerId())
          .projectId(batchDB.getProjectId())
          .content(
              Objects.isNull(batchDB.getSummaryForAnnotator())
                  ? ""
                  : batchDB.getSummaryForAnnotator())
          .type(BatchInstructionHistories.BatchInstructionType.SUMMARY_FOR_ANNOTATOR)
          .build());
    }

    if (Objects.nonNull(data.getSummaryForReviewer())
        && !Objects.equals(data.getSummaryForReviewer(), batchDB.getSummaryForReviewer())) {
      batchInstructionHistories.add(BatchInstructionHistories.builder()
          .batchId(batchDB.getId())
          .ownerId(batchDB.getOwnerId())
          .projectId(batchDB.getProjectId())
          .content(
              Objects.isNull(batchDB.getSummaryForReviewer())
                  ? ""
                  : batchDB.getSummaryForReviewer())
          .type(BatchInstructionHistories.BatchInstructionType.SUMMARY_FOR_REVIEWER)
          .build());
    }

    return batchInstructionHistories;
  }

  public Boolean canSubmitAmReview(Batch batch) {
    var taskList = taskListDao
        .listTaskListsByBatchIdAndType(batch.getId(), TaskList.TaskListType.ANNOTATION)
        .first();

    var taskCount = taskDao.countTasksByTaskListId(taskList.getId());

    var taskSessionCount = taskSessionMapper.selectJoinCount(JoinWrappers.lambda(TaskSession.class)
        .innerJoin(Job.class, Job::getId, TaskSession::getJobId)
        .eq(Job::getBatchId, batch.getId())
        .eq(TaskSession::getDeleted, false)
        .eq(Job::getDeleted, false)
        .in(Job::getStatus, List.of(Job.JobStatus.AM_AUDIT, Job.JobStatus.COMMITTED))
        .in(
            TaskSession::getStatus,
            List.of(
                TaskSession.TaskSessionStatus.PendingSpot, TaskSession.TaskSessionStatus.Finish)));

    return taskSessionCount >= taskCount;
  }

  @Transactional
  public void submitAmReview(Long batchId) {
    lambdaUpdate()
        .eq(Batch::getId, batchId)
        .set(Batch::getStatus, Batch.BatchStatus.CLIENT_REVIEW)
        .update();
    var jobIds = jobDao
        .listJobsByBatchId(batchId)
        .filter(job -> job.getStatus().equals(Job.JobStatus.AM_AUDIT))
        .map(Job::getId)
        .toList();
    jobMapper.update(
        null,
        new UpdateWrapper<Job>()
            .lambda()
            .eq(Job::getBatchId, batchId)
            .eq(Job::getDeleted, false)
            .eq(Job::getStatus, Job.JobStatus.AM_AUDIT)
            .set(Job::getStatus, Job.JobStatus.COMMITTED));
    if (!CollectionUtils.isEmpty(jobIds)) {
      amAuditSessionMapper.update(
          null,
          new UpdateWrapper<AmAuditSession>()
              .lambda()
              .in(AmAuditSession::getJobId, jobIds)
              .set(AmAuditSession::getResult, AmAuditSession.AmAuditSessionResult.APPROVED));
    }
  }

  public IPage<Batch> getBatchesByQueueManager(
      String searchParam, List<String> status, Integer page, Integer size) {
    Page<Batch> pg;
    if (page != null && size != null) {
      pg = Page.of(page, size);
    } else {
      pg = Page.of(0, 1);
    }

    return this.batchMapper.selectJoinPage(
        pg,
        Batch.class,
        JoinWrappers.lambda(Batch.class)
            .leftJoin(Project.class, Project::getId, Batch::getProjectId)
            .selectAll(Batch.class)
            .eq(Batch::getDeleted, false)
            .in(Batch::getStatus, status)
            .eq(Batch::getTaskType, Batch.TaskType.TASK)
            .eq(Project::getProjectType, Project.ProjectType.TRAINER_TEAM)
            .and(
                !searchParam.isEmpty(),
                b -> b.like(Batch::getName, searchParam).or().like(Project::getName, searchParam))
            .orderByDesc(Batch::getId));
  }

  @Deprecated
  public Batch launchBatch(Batch batch) {
    batch.setStatus(Batch.BatchStatus.ASSIGN_JOBS);
    batchMapper.update(
        null,
        new UpdateWrapper<Batch>()
            .lambda()
            .eq(Batch::getId, batch.getId())
            .set(Batch::getStatus, batch.getStatus())
            .set(Batch::getUpdatedAt, DateUtils.now()));
    if (batch.getTaskType().equals(Batch.TaskType.TASK)) {
      var jobExistAgain = jobDao.listJobsByBatchId(batch.getId());
      var job = jobExistAgain.first();
      var jobId = job.getId();
      if (Batch.DataType.CONTEXT.getValue().equals(batch.getDataType())
          || Batch.DataType.NO_NEED_TO_UPLOAD.getValue().equals(batch.getDataType())) {
        var batchSetting = batchDao.getBatchSettingById(batch.getId());
        batchSetting.setDistributeType(BatchSetting.DistributeType.SINGLE);
        batchDao.updateBatchSettingById(batchSetting);
        redisCache.set(
            "job_${jobId}:distributeType", BatchSetting.DistributeType.SINGLE.getValue());
        redisCache.set(
            "job_${jobId}:totalCount", batchSetting.getAnnotatingTimesAnnotationPerDatapoint());
        TaskList taskList = taskListDao
            .listTaskListsByBatchIdAndType(batch.getId(), TaskList.TaskListType.ANNOTATION)
            .first();
        var task = taskDao.getFirstTaskInTaskList(taskList.getId());
        task.ifPresent(taskEx -> {
          redisCache.set("job_${jobId}:taskId", taskEx.getId());
        });
      } else { // distributeType is: BatchSetting.DistributeType.RAW
        var batchSetting = batchDao.getBatchSettingById(batch.getId());
        redisCache.set("job_${jobId}:distributeType", BatchSetting.DistributeType.RAW.getValue());
        redisCache.set(
            "job_${jobId}:singleRepeat", batchSetting.getAnnotatingTimesAnnotationPerDatapoint());
        var taskIdList =
            jobTaskDao.selectTasksInJobInLimit(jobId, Math.min(7500, job.getAssignDataVolume()));
        redisCache.rSetAll(
            "job_${jobId}:cachedTaskIds",
            taskIdList.map(id -> (Object) id.toString()).toList());
      }
    }

    return batch;
  }

  public Optional<Batch> getBatchByTaskSessionId(Long sessionId) {
    return batchDao.getBatchByTaskSessionId(sessionId);
  }

  @Transactional
  public BatchDetailsDTO copyBatch(Batch batch, Batch originalBatch) {
    taskListDao.deleteTaskListByBatchId(batch.getId());
    var taskLists = TaskList.TaskListType.values().toList().stream()
        .map(t -> taskListDao.createTaskList(TaskList.builder()
            .batchId(batch.getId())
            .ownerId(batch.getOwnerId())
            .listType(t)
            .build()))
        .toList();

    var labelingTask = taskLists
        .filter(tl -> TaskList.TaskListType.LABEL == tl.getListType())
        .findFirst()
        .get();
    var originalLabelTaskList = taskListDao
        .listTaskListsByBatchIdAndType(originalBatch.getId(), TaskList.TaskListType.LABEL)
        .first();
    var originalQuestionTasks =
        taskService.listTasksByTaskListId(originalBatch, originalLabelTaskList.getId());

    Map<Long, TaskQuestion> questionIdNewQuestionDict = originalQuestionTasks
        .map(t -> {
          var newRes = resourceDao.copyResourceById(t.getResourceId());
          var task = Task.builder()
              .taskListId(labelingTask.getId())
              .resourceId(newRes.getId())
              .ownerId(batch.getOwnerId())
              .taskType(t.getTaskType())
              .originId(t.getOriginId())
              .isHybrid(t.getIsHybrid())
              .sort(t.getSort())
              .status(t.getStatus())
              .build();
          taskService.createTask(task);
          if (t.getIsHybrid()) {
            copyHybridTaskResource(task.getId(), t.getId());
          }

          var groups = questionGroupDao.getQuestionGroupsByTaskId(t.getId());
          var questions = taskQuestionDao.getQuestionsByTaskId(t.getId());
          var groupIdMap = groups
              .map(g -> {
                var questionGroup = QuestionGroup.builder()
                    .taskId(task.getId())
                    .name(g.getName())
                    .pageSize(g.getPageSize())
                    .sort(g.getSort())
                    .build();
                questionGroupDao.createQuestionGroup(questionGroup);
                return Pair.make(g.getId(), questionGroup.getId());
              })
              .toMap(Pair::getFirst, Pair::getSecond);
          return questions.map(q -> {
            var taskQuestion = TaskQuestion.builder()
                .taskId(task.getId())
                .ownerId(batch.getOwnerId())
                .groupId(groupIdMap.get(q.getGroupId()))
                .question(q.getQuestion())
                .questionType(q.getQuestionType())
                .details(q.getDetails())
                .answer(q.getAnswer())
                .answerRequired(q.getAnswerRequired())
                .sort(q.getSort())
                .build();
            taskQuestionDao.createTaskQuestion(taskQuestion);
            return Pair.make(q.getId(), taskQuestion);
          });
        })
        .flatMap(Function.identity())
        .toMap(Pair::getFirst, Pair::getSecond);

    questionIdNewQuestionDict.values().forEach(question -> {
      var questionId = question.getId();
      var questionDetails = question.getDetails();
      if (Objects.isNull(questionId) || StringUtils.isBlank(questionDetails)) {
        return;
      }
      try {
        var taskQuestionDetails =
            objectMapper.readValue(questionDetails, TaskQuestionDetailsDTO.class);
        var conditions = taskQuestionDetails.getConditions();
        if (Objects.isNull(conditions) || conditions.isEmpty()) {
          return;
        }
        var newConditions = conditions
            .map(condition -> {
              var target = condition.getTarget();
              if (Objects.isNull(target)) {
                return condition;
              }
              var newQ = questionIdNewQuestionDict.get(target);
              if (Objects.nonNull(newQ)) {
                condition.setTarget(newQ.getId());
              }
              return condition;
            })
            .toList();
        taskQuestionDetails.setConditions(newConditions);
        var newQuestionDetails = objectMapper.writeValueAsString(taskQuestionDetails);
        taskQuestionDao.updateQuestionDetails(questionId, newQuestionDetails);
      } catch (Exception e) {
        log.error("error taskQuestion detail: ${question.getId()} ${question.getDetails()} :", e);
      }
    });

    taskLists
        .filter(tl -> List.of(
                TaskList.TaskListType.GOOD_EXAMPLE,
                TaskList.TaskListType.BAD_EXAMPLE,
                TaskList.TaskListType.EXAM)
            .contains(tl.getListType()))
        .map(tl -> Pair.make(
            tl, taskListDao.listTaskListsByBatchIdAndType(originalBatch.getId(), tl.getListType())))
        .forEach(tltl -> taskService
            .getTasksByTaskListIds(tltl.getSecond().map(TaskList::getId).toList())
            .forEach(t -> {
              Long realOriginId;
              if (tltl.getFirst().getListType() == TaskList.TaskListType.EXAM) {
                realOriginId = t.getOriginId();
              } else if (t.getIsHybrid()) {
                realOriginId =
                    taskService.getLabelingHybridTaskIdByTaskListId(labelingTask.getId());
              } else {
                realOriginId = taskService.getLabelingNonHybridTaskIdByResId(
                    labelingTask.getId(), t.getResourceId());
              }
              var newRes = resourceDao.copyResourceById(t.getResourceId());
              var task = Task.builder()
                  .taskListId(tltl.getFirst().getId())
                  .resourceId(newRes.getId())
                  .ownerId(batch.getOwnerId())
                  .taskType(t.getTaskType())
                  .originId(realOriginId)
                  .isHybrid(t.getIsHybrid())
                  .sort(t.getSort())
                  .status(t.getStatus())
                  .build();
              taskService.createTask(task);
              if (t.getIsHybrid()) {
                copyHybridTaskResource(task.getId(), t.getId());
              }
              var answers = questionAnswerDao.getExampleAnswerByTaskId(t.getId());
              questionAnswerDao.batchCreateOrUpdateAnswers(answers
                  .map(a -> QuestionAnswer.builder()
                      .taskId(task.getId())
                      .questionId(
                          questionIdNewQuestionDict.get(a.getQuestionId()).getId())
                      .ownerId(batch.getOwnerId())
                      .answer(a.getAnswer())
                      .explanation(a.getExplanation())
                      .build())
                  .toList());
            }));

    batchSampleDao.deleteBatchSampleByBatchId(batch.getId());
    var originalBatchSamples = batchSampleDao.listBatchSamplesByBatch(originalBatch);
    var samples = originalBatchSamples
        .map(bs -> BatchSample.builder()
            .batchId(batch.getId())
            .resourceId(bs.getResourceId())
            .ownerId(batch.getOwnerId())
            .build())
        .toList();
    var batchSamples = samples.map(batchSampleDao::createBatchSample).toList();

    var batchNDAs = batchNDADao.getNDAsByBatch(originalBatch.getId());
    batchNDADao.batchUpdateNda(batch.getId(), List.of(), List.of(), batchNDAs);

    var newStatus = List.of(
                Batch.BatchStatus.DRAFT,
                Batch.BatchStatus.SAMPLE_UPLOADED,
                Batch.BatchStatus.TASK_CREATED,
                Batch.BatchStatus.INSTRUCTION_CREATED,
                Batch.BatchStatus.EXAM_CREATED,
                Batch.BatchStatus.CHECKING,
                Batch.BatchStatus.CALIBRATING)
            .contains(originalBatch.getStatus())
        ? originalBatch.getStatus()
        : Batch.BatchStatus.CALIBRATING;

    var newBatch = Batch.builder()
        .id(batch.getId())
        .name(batch.getName())
        .ownerId(originalBatch.getOwnerId())
        .projectId(originalBatch.getProjectId())
        .summary(originalBatch.getSummary())
        .summaryForAnnotator(originalBatch.getSummaryForAnnotator())
        .summaryForReviewer(originalBatch.getSummaryForReviewer())
        .description(originalBatch.getDescription())
        .extraInfo(originalBatch.getExtraInfo())
        .labelType(originalBatch.getLabelType())
        .labelBody(originalBatch.getLabelBody())
        .status(newStatus)
        .deadline(originalBatch.getDeadline())
        .dataType(originalBatch.getDataType())
        .requiredAccuracy(originalBatch.getRequiredAccuracy())
        .requiredReviewAccuracy(originalBatch.getRequiredReviewAccuracy())
        .spotAudit(originalBatch.getSpotAudit())
        .requiredRegions(originalBatch.getRequiredRegions())
        .requiredLanguages(originalBatch.getRequiredLanguages())
        .requiredSkills(originalBatch.getRequiredSkills())
        .labelingRepeat(originalBatch.getLabelingRepeat())
        .reviewRepeat(originalBatch.getReviewRepeat())
        .build();
    updateById(newBatch);

    return BatchDetailsDTO.builder().batch(newBatch).samples(batchSamples).build();
  }

  @Transactional
  public BatchDetailsDTO copyBatchV2(Batch originalBatch) {

    var batch = new Batch();
    BeanUtils.copyProperties(originalBatch, batch);
    batch.setId(null);
    batch.setStatus(Batch.BatchStatus.DRAFT);
    batch.setName("");
    batch.setCreatedAt(null);
    batch.setUpdatedAt(null);
    this.createBatch(batch);

    var oriRewardRecords = batchRewardRecordDao.listByBatchId(originalBatch.getId());
    oriRewardRecords.forEach(rewardRecord -> {
      rewardRecord.setId(null);
      rewardRecord.setPoolStatus(BatchRewardRecord.PoolStatus.DRAFT);
      rewardRecord.setBatchId(batch.getId());
      batchRewardRecordDao.save(rewardRecord);
    });

    BatchSetting originalSetting = this.batchDao.getBatchSettingById(originalBatch.getId());
    BatchSetting newBatchSetting = new BatchSetting();
    BeanUtils.copyProperties(originalSetting, newBatchSetting);
    newBatchSetting.setId(null);
    newBatchSetting.setBatchId(batch.getId());
    this.batchDao.saveBatchSetting(newBatchSetting);

    List<BatchAccessRequirement> accessRequirements =
        batchAccessRequirementDao.getBatchAccessRequirementsByBatchId(originalBatch.getId());
    accessRequirements.forEach(x -> {
      x.setId(null);
      x.setBatchId(batch.getId());
      batchAccessRequirementDao.create(x);
    });

    //    var originalBatchSamples = batchSampleDao.listBatchSamplesByBatch(originalBatch);
    //    var samples = originalBatchSamples
    //        .map(bs -> BatchSample.builder()
    //            .batchId(batch.getId())
    //            .resourceId(bs.getResourceId())
    //            .ownerId(batch.getOwnerId())
    //            .build())
    //        .toList();
    //    samples.forEach(batchSampleDao::createBatchSample);

    var batchNDAs = batchNDADao.getNDAsByBatch(originalBatch.getId());
    batchNDADao.batchUpdateNda(batch.getId(), List.of(), List.of(), batchNDAs);

    // TaskList.TaskListType[] values = TaskList.TaskListType.values();
    // var taskLists = values.toList().stream()
    // .map(t -> taskListDao.createTaskList(TaskList.builder().batchId(batch.getId())
    // .ownerId(batch.getOwnerId()).listType(t).build()))
    // .toList();

    var labelingTask = taskListDao
        .listTaskListsByBatchIdAndType(batch.getId(), TaskList.TaskListType.LABEL)
        .first();
    var originalLabelTaskList = taskListDao
        .listTaskListsByBatchIdAndType(originalBatch.getId(), TaskList.TaskListType.LABEL)
        .first();
    var originalQuestionTasks =
        taskService.listTasksByTaskListId(originalBatch, originalLabelTaskList.getId());

    Map<Long, TaskQuestion> questionIdNewQuestionDict = originalQuestionTasks
        .map(t -> {
          var newRes = resourceDao.copyResourceById(t.getResourceId());
          var task = Task.builder()
              .taskListId(labelingTask.getId())
              .resourceId(newRes.getId())
              .ownerId(batch.getOwnerId())
              .taskType(t.getTaskType())
              .originId(t.getOriginId())
              .isHybrid(t.getIsHybrid())
              .sort(t.getSort())
              .status(t.getStatus())
              .build();
          taskService.createTask(task);
          if (t.getIsHybrid()) {
            copyHybridTaskResource(task.getId(), t.getId());
          }

          var groups = questionGroupDao.getQuestionGroupsByTaskId(t.getId());
          var questions = taskQuestionDao.getQuestionsByTaskId(t.getId());
          var groupIdMap = groups
              .map(g -> {
                var questionGroup = QuestionGroup.builder()
                    .taskId(task.getId())
                    .name(g.getName())
                    .pageSize(g.getPageSize())
                    .sort(g.getSort())
                    .build();
                questionGroupDao.createQuestionGroup(questionGroup);
                return Pair.make(g.getId(), questionGroup.getId());
              })
              .toMap(Pair::getFirst, Pair::getSecond);
          return questions.map(q -> {
            var taskQuestion = TaskQuestion.builder()
                .taskId(task.getId())
                .ownerId(batch.getOwnerId())
                .groupId(groupIdMap.get(q.getGroupId()))
                .question(q.getQuestion())
                .questionType(q.getQuestionType())
                .details(q.getDetails())
                .answer(q.getAnswer())
                .answerRequired(q.getAnswerRequired())
                .sort(q.getSort())
                .build();
            taskQuestionDao.createTaskQuestion(taskQuestion);
            return Pair.make(q.getId(), taskQuestion);
          });
        })
        .flatMap(Function.identity())
        .toMap(Pair::getFirst, Pair::getSecond);

    questionIdNewQuestionDict.values().forEach(question -> {
      var questionId = question.getId();
      var questionDetails = question.getDetails();
      if (Objects.isNull(questionId) || StringUtils.isBlank(questionDetails)) {
        return;
      }
      try {
        var taskQuestionDetails =
            objectMapper.readValue(questionDetails, TaskQuestionDetailsDTO.class);
        var conditions = taskQuestionDetails.getConditions();
        if (Objects.isNull(conditions) || conditions.isEmpty()) {
          return;
        }
        var newConditions = conditions
            .map(condition -> {
              var target = condition.getTarget();
              if (Objects.isNull(target)) {
                return condition;
              }
              var newQ = questionIdNewQuestionDict.get(target);
              if (Objects.nonNull(newQ)) {
                condition.setTarget(newQ.getId());
              }
              return condition;
            })
            .toList();
        taskQuestionDetails.setConditions(newConditions);
        var newQuestionDetails = objectMapper.writeValueAsString(taskQuestionDetails);
        taskQuestionDao.updateQuestionDetails(questionId, newQuestionDetails);
      } catch (Exception e) {
        log.error("error taskQuestion detail: ${question.getId()} ${question.getDetails()} :", e);
      }
    });

    List<TaskList> taskLists = this.taskListDao.listTaskListsByBatchId(batch.getId());
    taskLists
        .filter(tl -> (Batch.ExamType.QA == originalBatch.getExamType()
                ? List.of(TaskList.TaskListType.EXAM)
                : List.of(
                            Batch.DataType.CONTEXT.getValue(),
                            Batch.DataType.NO_NEED_TO_UPLOAD.getValue())
                        .contains(originalBatch.getDataType())
                    ? List.of(TaskList.TaskListType.ANNOTATION)
                    : List.of())
            .contains(tl.getListType()))
        .map(tl -> Pair.make(
            tl, taskListDao.listTaskListsByBatchIdAndType(originalBatch.getId(), tl.getListType())))
        .forEach(tltl -> taskService
            .getTasksByTaskListIds(tltl.getSecond().map(TaskList::getId).toList())
            .forEach(t -> {
              Long realOriginId;
              if (tltl.getFirst().getListType() == TaskList.TaskListType.EXAM) {
                realOriginId = t.getOriginId();
              } else if (t.getIsHybrid()) {
                realOriginId =
                    taskService.getLabelingHybridTaskIdByTaskListId(labelingTask.getId());
              } else {
                realOriginId = taskService.getLabelingNonHybridTaskIdByResId(
                    labelingTask.getId(), t.getResourceId());
              }
              var newRes = resourceDao.copyResourceById(t.getResourceId());
              var task = Task.builder()
                  .taskListId(tltl.getFirst().getId())
                  .resourceId(newRes.getId())
                  .ownerId(batch.getOwnerId())
                  .taskType(t.getTaskType())
                  .originId(realOriginId)
                  .isHybrid(t.getIsHybrid())
                  .sort(t.getSort())
                  .status(t.getStatus())
                  .build();
              taskService.createTask(task);
              if (t.getIsHybrid()) {
                copyHybridTaskResource(task.getId(), t.getId());
              }
              var answers = questionAnswerDao.getExampleAnswerByTaskId(t.getId());
              questionAnswerDao.batchCreateOrUpdateAnswers(answers
                  .map(a -> QuestionAnswer.builder()
                      .taskId(task.getId())
                      .questionId(
                          questionIdNewQuestionDict.get(a.getQuestionId()).getId())
                      .ownerId(batch.getOwnerId())
                      .answer(a.getAnswer())
                      .explanation(a.getExplanation())
                      .build())
                  .toList());
            }));

    return this.getBatchDetails(batch.getId());
  }

  @Transactional
  public void requesterAccept(Long batchId, List<Long> committedJobIds) {
    batchMapper.update(
        null,
        new UpdateWrapper<Batch>()
            .lambda()
            .eq(Batch::getId, batchId)
            .set(Batch::getStatus, Batch.BatchStatus.SETTLING)
            .set(Batch::getUpdatedAt, DateUtils.now()));

    for (Long jobId : committedJobIds) {
      jobMapper.update(
          null,
          new UpdateWrapper<Job>()
              .lambda()
              .eq(Job::getId, jobId)
              .set(Job::getStatus, Job.JobStatus.SETTLING)
              .set(Job::getUpdatedAt, DateUtils.now()));
      // TODO 等待point修改
      // seasonJobMapper.update(null, new
      // UpdateWrapper<SeasonJob>().lambda().eq(SeasonJob::getJobId, jobId)
      // .set(SeasonJob::getStatus, SeasonJob.PointsStatus.Pending).set(SeasonJob::getUpdatedAt,
      // DateUtils.now()));
    }
    var jobIds = jobDao.listJobsByBatchId(batchId).map(Job::getId).toList();
    clientAuditSessionMapper.update(
        null,
        new UpdateWrapper<ClientAuditSession>()
            .lambda()
            .in(ClientAuditSession::getJobId, jobIds)
            .set(
                ClientAuditSession::getResult, ClientAuditSession.ClientAuditSessionResult.APPROVED)
            .set(ClientAuditSession::getUpdatedAt, DateUtils.now()));
  }

  @Transactional
  public void requesterReject(Long batchId, List<Long> committedJobIds) {
    batchMapper.update(
        null,
        new UpdateWrapper<Batch>()
            .lambda()
            .eq(Batch::getId, batchId)
            .set(Batch::getStatus, Batch.BatchStatus.CLIENT_REJECTED)
            .set(Batch::getUpdatedAt, DateUtils.now()));

    for (Long jobId : committedJobIds) {
      jobMapper.update(
          null,
          new UpdateWrapper<Job>()
              .lambda()
              .eq(Job::getId, jobId)
              .set(Job::getStatus, Job.JobStatus.AM_AUDIT)
              .set(Job::getUpdatedAt, DateUtils.now()));
    }

    jobMapper.update(
        null,
        new UpdateWrapper<Job>()
            .lambda()
            .eq(Job::getBatchId, batchId)
            .eq(Job::getAmReviewStatus, TaskSessionAmReviewStatus.APPROVED)
            .eq(Job::getDeleted, false)
            .set(Job::getAmReviewStatus, TaskSessionAmReviewStatus.NONE)
            .set(Job::getUpdatedAt, DateUtils.now()));

    var jobIds = jobDao.listJobsByBatchId(batchId).map(Job::getId).toList();

    taskSessionMapper.update(
        null,
        new UpdateWrapper<TaskSession>()
            .lambda()
            .in(TaskSession::getJobId, jobIds)
            .set(TaskSession::getAmReviewStatus, TaskSessionAmReviewStatus.NONE)
            .set(TaskSession::getClientReviewStatus, TaskSessionAmReviewStatus.NONE)
            .set(TaskSession::getUpdatedAt, DateUtils.now()));

    clientAuditSessionMapper.update(
        null,
        new UpdateWrapper<ClientAuditSession>()
            .lambda()
            .in(ClientAuditSession::getJobId, jobIds)
            .set(
                ClientAuditSession::getResult, ClientAuditSession.ClientAuditSessionResult.REJECTED)
            .set(ClientAuditSession::getUpdatedAt, DateUtils.now()));
  }

  public Pair<Long, Long> getRequesterReviewData(Long batchId) {
    var clientApprovedCount =
        taskSessionMapper.selectJoinCount(JoinWrappers.lambda(TaskSession.class)
            .innerJoin(Job.class, Job::getId, TaskSession::getJobId)
            .eq(Job::getBatchId, batchId)
            .eq(TaskSession::getDeleted, false)
            .eq(TaskSession::getClientReviewStatus, TaskSessionAmReviewStatus.APPROVED));
    var clientRevisedCount =
        taskSessionMapper.selectJoinCount(JoinWrappers.lambda(TaskSession.class)
            .innerJoin(Job.class, Job::getId, TaskSession::getJobId)
            .eq(Job::getBatchId, batchId)
            .eq(TaskSession::getDeleted, false)
            .eq(TaskSession::getClientReviewStatus, TaskSessionAmReviewStatus.REJECTED));
    return Pair.make(clientApprovedCount, clientRevisedCount);
  }

  public Pair<Long, Long> getAMReviewData(Long batchId) {
    var amApprovedCount = taskSessionMapper.selectJoinCount(JoinWrappers.lambda(TaskSession.class)
        .innerJoin(Job.class, Job::getId, TaskSession::getJobId)
        .eq(Job::getBatchId, batchId)
        .ne(Job::getStatus, Job.JobStatus.DROPPED)
        .ne(Job::getStatus, Job.JobStatus.REJECTED)
        .eq(TaskSession::getDeleted, false)
        .eq(TaskSession::getAmReviewStatus, TaskSessionAmReviewStatus.APPROVED));
    var amRevisedCount = taskSessionMapper.selectJoinCount(JoinWrappers.lambda(TaskSession.class)
        .innerJoin(Job.class, Job::getId, TaskSession::getJobId)
        .eq(Job::getBatchId, batchId)
        .ne(Job::getStatus, Job.JobStatus.DROPPED)
        .ne(Job::getStatus, Job.JobStatus.REJECTED)
        .eq(TaskSession::getDeleted, false)
        .eq(TaskSession::getAmReviewStatus, TaskSessionAmReviewStatus.REJECTED));
    return Pair.make(amApprovedCount, amRevisedCount);
  }

  public Long countUnAllocatedTask(Long batchId) {
    var batch = this.batchDao.getBatchById(batchId);
    if (batch.isEmpty()) {
      return 0L;
    }

    TaskList taskList = taskListDao
        .listTaskListsByBatchIdAndType(batch.get().getId(), TaskList.TaskListType.ANNOTATION)
        .first();

    Long relativeTaskCount = taskDao.countTasksByTaskListId(taskList.getId());

    var existJobs = jobDao.listExistingJobsByBatchId(batchId);

    Long existTaskCount =
        jobTaskDao.countJobTaskByJobIdList(existJobs.map(Job::getId).toList());

    return relativeTaskCount - existTaskCount;
  }

  private void copyHybridTaskResource(Long newTaskId, Long sourceId) {
    var existRecs = hybridTaskResourceDao.getHybridTaskResourcesByTaskId(sourceId);
    if (!CollectionUtils.isEmpty(existRecs)) {
      existRecs.stream()
          .map(sourceRec -> HybridTaskResource.builder()
              .taskId(newTaskId)
              .resourceId(sourceRec.getResourceId())
              .sort(sourceRec.getSort())
              .build())
          .forEach(hybridTaskResourceDao::createHybridTaskResource);
    }
  }

  public Page<Batch> getAcademyPages(
      CertificateCategory type,
      String name,
      CertificateType courseType,
      Batch.BatchStatus status,
      String knowledge,
      List<Batch.CourseDifficulty> courseDifficulty,
      Integer page,
      Integer size) {
    return this.batchDao.getAcademyPages(
        type, name, courseType, status, knowledge, courseDifficulty, page, size);
  }

  public PageResult<Batch> getAcademyPagesByAvailable(
      Long userId,
      Boolean available,
      CertificateCategory category,
      CertificateType courseType,
      String knowledge,
      List<Batch.CourseDifficulty> courseDifficulty,
      String name,
      Integer page,
      Integer size) {

    return this.batchDao.getAcademyPagesByAvailable(
        userId, available, category, courseType, knowledge, courseDifficulty, name, page, size);
  }

  public List<Batch> canJoinAcademy(Long userId, Long certificateId) {
    return this.batchDao.canJoinAcademy(userId, certificateId);
  }

  public List<Batch> canJoinTask(Long userId, Long certificateId) {
    return this.batchDao.canJoinTask(userId, certificateId);
  }

  public List<Batch> hasLevelUp(
      Long userId,
      Long certificateId,
      CertificateCategory category,
      CertificateType certificateType) {
    return this.batchDao.hasLevelUp(userId, certificateId, category, certificateType);
  }

  public List<Batch> previewCanJoinTaskPreview(Long userId, Long certificateId) {
    return this.batchDao.previewCanJoinTaskPreview(userId, certificateId);
  }

  public Boolean hasAcademyBatchByName(String name, Long batchId) {
    return this.batchDao.countAcademyBatchByName(name, batchId) > 0;
  }

  public void clearExamSetting(Long batchId) {
    this.batchMapper.update(
        null,
        new UpdateWrapper<Batch>()
            .lambda()
            .eq(Batch::getId, batchId)
            .set(Batch::getUpdatedAt, DateUtils.now())
            .set(Batch::getShowAnswer, null)
            .set(Batch::getExamNenominator, null)
            .set(Batch::getExamNumerator, null)
            .set(Batch::getExamNumeratorForReviewing, null)
            .set(Batch::getEstExamTime, 0));
  }

  public Batch getBatchByJobId(Long jobId) {
    return this.batchDao.getByJobId(jobId);
  }

  public BatchSetting getBatchSettingByBatchId(Long batchId) {
    return this.batchDao.getBatchSettingById(batchId);
  }

  public boolean isBatchJobPausing(Long id) {
    var jobs = jobDao.listJobsByBatchId(id);
    if (jobs.isEmpty()) {
      return false;
    }
    return jobs.first().getStatus() == Job.JobStatus.PAUSING;
  }

  public List<BatchRewardRecord> getBatchRewardRecords(Long id) {
    return batchRewardRecordDao.listByBatchId(id);
  }
}
