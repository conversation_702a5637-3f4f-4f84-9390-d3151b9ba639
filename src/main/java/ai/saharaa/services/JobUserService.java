package ai.saharaa.services;

import static ai.saharaa.model.JobUser.JobUserRole.LABELER;
import static ai.saharaa.model.JobUser.JobUserRole.REVIEWER;
import static ai.saharaa.model.JobUser.JobUserRole.SPOTTER;
import static ai.saharaa.model.JobUser.JobUserRole.UNALLOCATED;
import static ai.saharaa.utils.Constants.Cache.TASK_TOKEN_DISTRIBUTION_COMPLETE_LABELING;
import static ai.saharaa.utils.Constants.Cache.TASK_TOKEN_DISTRIBUTION_COMPLETE_REVIEWING;
import static ai.saharaa.utils.Constants.ROLE_USER;

import ai.saharaa.common.cache.IGlobalCache;
import ai.saharaa.common.contracts.DSPReward;
import ai.saharaa.config.ChainConfig;
import ai.saharaa.daos.*;
import ai.saharaa.daos.newTasks.BatchRewardRecordDao;
import ai.saharaa.daos.newTasks.NewTasksDao;
import ai.saharaa.daos.newTasks.RewardTokenInfoDao;
import ai.saharaa.daos.newTasks.UserTokenTaskRewardsDao;
import ai.saharaa.distribution.CommonCounterManager;
import ai.saharaa.distribution.Contants.Constants;
import ai.saharaa.dto.job.JobFields;
import ai.saharaa.dto.job.JobInviteDTO;
import ai.saharaa.dto.job.JobUserDetailDTO;
import ai.saharaa.dto.job.JobUserInviteDTO;
import ai.saharaa.dto.job.JobUserRoleAssignDTO;
import ai.saharaa.dto.sign.UserActivateJobRoleSignMessageDTO;
import ai.saharaa.dto.sign.data.UserActivateJobRole;
import ai.saharaa.dto.task.JobUserPointsGroupedDataDTO;
import ai.saharaa.dto.user.UserDTO;
import ai.saharaa.enums.RewardTokenType;
import ai.saharaa.mappers.*;
import ai.saharaa.model.*;
import ai.saharaa.model.JobUser.JobUserRole;
import ai.saharaa.model.newTasks.BatchRewardRecord;
import ai.saharaa.model.newTasks.RewardAllocation;
import ai.saharaa.model.newTasks.RewardTokenInfo;
import ai.saharaa.model.newTasks.UserTokenTaskRewards;
import ai.saharaa.services.newTasks.MerkleTreeService;
import ai.saharaa.utils.ControllerUtils;
import ai.saharaa.utils.DateUtils;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.github.yulichang.toolkit.JoinWrappers;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.math.MathContext;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.web3j.abi.datatypes.generated.Uint64;

@Service
@Slf4j
public class JobUserService extends ServiceImpl<JobUserMapper, JobUser> {
  private final JobMapper jobMapper;
  private final NDASignRecordMapper ndaSignRecordMapper;
  private final ExamSessionMapper examSessionMapper;
  private final BatchNDADao batchNDADao;
  private final UserMapper userMapper;
  private final NodeMapper nodeMapper;
  private final JobUserMapper jobUserMapper;
  private final JobUserDao jobUserDao;
  private final ChainConfig chainConfig;
  private final NotificationService notificationService;
  private final IndividualsDao individualsDao;
  private final IGlobalCache cache;
  private final JobDao jobDao;
  private final BatchDao batchDao;
  private final NewTasksDao newTasksDao;
  private final TaskSessionDao taskSessionDao;
  private final ReviewSessionDao reviewSessionDao;
  private final JobUserPointPreCalcService jobUserPointPreCalcService;
  private final CommonCounterManager commonCounterManager;
  private final TaskSessionMapper taskSessionMapper;
  private final ReviewSessionMapper reviewSessionMapper;
  private final MerkleTreeService merkleTreeService;
  private final UserTokenTaskRewardsDao userTokenTaskRewardsDao;
  private final BatchRewardRecordDao batchRewardRecordDao;
  private final DSPReward dspRewardContract;
  private final UserDao userDao;
  private final RewardTokenInfoDao rewardTokenInfoDao;

  public JobUserService(
      JobMapper jobMapper,
      NDASignRecordMapper ndaSignRecordMapper,
      ExamSessionMapper examSessionMapper,
      BatchNDADao batchNDADao,
      NodeMapper nodeMapper,
      JobUserMapper jobUserMapper,
      UserMapper userMapper,
      JobUserDao jobUserDao,
      NewTasksDao newTasksDao,
      ChainConfig chainConfig,
      NotificationService notificationService,
      IndividualsDao individualsDao,
      JobDao jobDao,
      BatchDao batchDao,
      IGlobalCache cache,
      TaskSessionDao taskSessionDao,
      ReviewSessionDao reviewSessionDao,
      JobUserPointPreCalcService jobUserPointPreCalcService,
      CommonCounterManager commonCounterManager,
      TaskSessionMapper taskSessionMapper,
      ReviewSessionMapper reviewSessionMapper,
      MerkleTreeService merkleTreeService,
      UserTokenTaskRewardsDao userTokenTaskRewardsDao,
      BatchRewardRecordDao batchRewardRecordDao,
      UserDao userDao,
      DSPReward dspRewardContract,
      RewardTokenInfoDao rewardTokenInfoDao) {
    this.cache = cache;
    this.jobMapper = jobMapper;
    this.ndaSignRecordMapper = ndaSignRecordMapper;
    this.batchNDADao = batchNDADao;
    this.userMapper = userMapper;
    this.nodeMapper = nodeMapper;
    this.jobUserMapper = jobUserMapper;
    this.examSessionMapper = examSessionMapper;
    this.jobUserDao = jobUserDao;
    this.chainConfig = chainConfig;
    this.newTasksDao = newTasksDao;
    this.notificationService = notificationService;
    this.individualsDao = individualsDao;
    this.jobDao = jobDao;
    this.batchDao = batchDao;
    this.taskSessionDao = taskSessionDao;
    this.reviewSessionDao = reviewSessionDao;
    this.jobUserPointPreCalcService = jobUserPointPreCalcService;
    this.commonCounterManager = commonCounterManager;
    this.taskSessionMapper = taskSessionMapper;
    this.reviewSessionMapper = reviewSessionMapper;
    this.merkleTreeService = merkleTreeService;
    this.userTokenTaskRewardsDao = userTokenTaskRewardsDao;
    this.batchRewardRecordDao = batchRewardRecordDao;
    this.userDao = userDao;
    this.rewardTokenInfoDao = rewardTokenInfoDao;
    this.dspRewardContract = dspRewardContract;
  }

  public IPage<User> getAvailableWorkers(Long jobId, Integer page, Integer limit, Long curId) {
    Optional<Node> myNode = nodeMapper
        .selectOne(new QueryWrapper<Node>().lambda().eq(Node::getNodeManagerId, curId))
        .asOpt();
    if (myNode.isEmpty()) {
      throw ControllerUtils.badRequest("invalid data");
    }
    var nodeId = myNode.get().getId();
    List<JobUser> jobUsers = jobUserMapper.selectList(new QueryWrapper<JobUser>()
        .lambda()
        // .eq(JobUser::getRole, PRE_EXAM)
        .eq(JobUser::getTaskListSessionId, jobId)
        .eq(JobUser::getDisabled, false)
        .eq(JobUser::getDeleted, false));
    return userMapper.selectPage(
        Page.of(page, limit),
        new QueryWrapper<User>()
            .lambda()
            .eq(User::getRole, ROLE_USER)
            .eq(User::getNodeId, nodeId)
            .notIn(
                !(jobUsers == null || jobUsers.isEmpty()),
                User::getId,
                jobUsers.map(JobUser::getUserId).collect(Collectors.toList())));
  }

  public Long getJobActiveCountThisWeek(Long userId, Timestamp startTime) {
    var activeCountJob = jobUserDao.getLatestJobUserByUserIdWithCreateTimeRange(userId, startTime);

    return activeCountJob;
  }

  public IPage<User> getExamInvitableWorkers(Long jobId, Integer page, Integer limit, Long curId) {
    Optional<Node> myNode = nodeMapper
        .selectOne(new QueryWrapper<Node>().lambda().eq(Node::getNodeManagerId, curId))
        .asOpt();
    if (myNode.isEmpty()) {
      throw ControllerUtils.badRequest("invalid data");
    }
    var nodeId = myNode.get().getId();
    List<JobUser> jobUsers = jobUserMapper.selectList(new QueryWrapper<JobUser>()
        .lambda()
        .eq(JobUser::getTaskListSessionId, jobId)
        .eq(JobUser::getDeleted, false));
    return userMapper.selectPage(
        Page.of(page, limit),
        new QueryWrapper<User>()
            .lambda()
            .eq(User::getRole, ROLE_USER)
            .eq(User::getNodeId, nodeId)
            .notIn(
                !(jobUsers == null || jobUsers.isEmpty()),
                User::getId,
                jobUsers.map(JobUser::getUserId).collect(Collectors.toList())));
  }

  @Transactional
  public Boolean doInviteUserToJob(JobUserInviteDTO body, JobUser.JobUserRole role, Long curId) {
    if (body.getUserIdList().isEmpty() || null == body.getJobId()) {
      throw ControllerUtils.badRequest("invalid data");
    }
    Optional<Job> targetJob = jobMapper
        .selectOne(new QueryWrapper<Job>()
            .lambda()
            .eq(Job::getId, body.getJobId())
            .eq(Job::getDeleted, false))
        .asOpt();
    if (targetJob.isEmpty()) {
      throw ControllerUtils.badRequest("invalid data");
    }
    // check these users already joined or not
    List<JobUser> exits = jobUserMapper.selectList(new QueryWrapper<JobUser>()
        .lambda()
        .in(
            !(body.getUserIdList() == null || body.getUserIdList().isEmpty()),
            JobUser::getUserId,
            body.getUserIdList())
        .eq(JobUser::getTaskListSessionId, body.getJobId())
        .eq(JobUser::getDeleted, false));
    if (exits != null && !exits.isEmpty()) {
      throw ControllerUtils.badRequest("invalid data");
    }
    // check these users are members of the node
    List<User> usersInNode = userMapper.selectList(new QueryWrapper<User>()
        .lambda()
        .eq(User::getRole, ROLE_USER)
        .eq(User::getNodeId, targetJob.get().getNodeId())
        .in(User::getId, body.getUserIdList()));
    if (usersInNode.size() != body.getUserIdList().size()) { // some user are not member or with
      // wrong role
      throw ControllerUtils.badRequest("invalid data");
    }

    var status = role;
    if (Objects.isNull(role)) {
      var hasNda = batchNDADao.getNdaCountByBatchId(targetJob.get().getBatchId()) > 0;
      status = hasNda ? JobUserRole.NDA_SIGNING : JobUserRole.INVITATION;
    }

    for (User user : usersInNode) {
      JobUser userInvitor = JobUser.builder()
          .userId(user.getId())
          .taskListSessionId(body.getJobId())
          .role(status)
          .build();
      jobUserDao.createJobUser(userInvitor);
    }
    for (Long userId : body.getUserIdList()) {
      notificationService.noticeUserNewJob(curId, userId, body.getJobId());
    }
    return true;
  }

  public IPage<JobInviteDTO> getMyInvites(Integer page, Integer limit, Long curId) {
    IPage<JobUser> res = jobUserMapper.selectPage(
        Page.of(page, limit),
        new QueryWrapper<JobUser>()
            .lambda()
            .eq(JobUser::getUserId, curId)
            .eq(JobUser::getRole, JobUserRole.INVITATION)
            .eq(JobUser::getDeleted, false)
            .eq(JobUser::getDisabled, false));
    if (!res.getRecords().isEmpty()) {
      Map<Long, Job> jobMap = jobMapper
          .selectList(new QueryWrapper<Job>()
              .lambda()
              .in(
                  Job::getId,
                  res.getRecords().map(JobUser::getTaskListSessionId).collect(Collectors.toList())))
          .toMap(Job::getId, Function.identity());
      return res.convert(r -> JobInviteDTO.builder()
          .invite(r)
          .job(jobMap.get(r.getTaskListSessionId()))
          .build());
    }
    return res.convert(r -> JobInviteDTO.builder().invite(r).build());
  }

  @Transactional
  public List<JobUser> assignWorkerRoles(Long jobId, Long uid, List<JobUserRoleAssignDTO> dto) {
    Job targetJob = jobMapper.selectOne(
        new QueryWrapper<Job>().lambda().eq(Job::getId, jobId).eq(Job::getDeleted, false));

    Node node = this.nodeMapper.selectOne(new QueryWrapper<Node>()
        .lambda()
        .eq(Node::getDeleted, Boolean.FALSE)
        .eq(Node::getId, targetJob.getNodeId()));

    if (!Objects.equals(node.getNodeManagerId(), uid)) {
      throw ControllerUtils.badRequest("not your job");
    }

    List<Long> assigningUserIdList =
        dto.map(JobUserRoleAssignDTO::getUserId).collect(Collectors.toList());
    List<JobUser> assigningUsers = jobUserMapper.selectList(new QueryWrapper<JobUser>()
        .lambda()
        .eq(JobUser::getRole, UNALLOCATED)
        .eq(JobUser::getDeleted, false)
        .eq(JobUser::getDisabled, false)
        .eq(JobUser::getTaskListSessionId, jobId)
        .in(JobUser::getUserId, assigningUserIdList));
    if (assigningUsers.size() != dto.size()) { // some assignment are not valid
      throw ControllerUtils.badRequest("invalid data");
    }
    Map<Long, JobUserRole> newRoleMap =
        dto.toMap(JobUserRoleAssignDTO::getUserId, JobUserRoleAssignDTO::getRole);
    assigningUsers.forEach(jUser -> {
      var newRole = newRoleMap.get(jUser.getUserId());
      if (newRole == JobUserRole.LABELER
          || newRole == JobUserRole.REVIEWER
          || newRole == JobUserRole.SPOTTER) {
        jobUserDao.updateJobUserById(
            JobUser.builder().id(jUser.getId()).role(newRole).build());
      }
    });
    for (JobUserRoleAssignDTO assign : dto) {
      notificationService.noticeUserJobRoleAssigned(assign.getUserId(), jobId, assign.getRole());
    }
    return assigningUsers;
  }

  public Optional<JobUser> getJobUserByJobAndUserId(Long jobId, Long uid) {
    return jobUserMapper
        .selectOne(new QueryWrapper<JobUser>()
            .lambda()
            .eq(JobUser::getTaskListSessionId, jobId)
            .eq(JobUser::getUserId, uid)
            .eq(JobUser::getDisabled, false)
            .eq(JobUser::getDeleted, false)
            .orderByDesc(JobUser::getCreatedAt)
            .last("limit 1"))
        .asOpt();
  }

  public Optional<JobUser> getJobUserByJobAndUserIdAndRole(Long jobId, Long uid, JobUserRole role) {
    return jobUserMapper
        .selectOne(new QueryWrapper<JobUser>()
            .lambda()
            .eq(JobUser::getTaskListSessionId, jobId)
            .eq(JobUser::getUserId, uid)
            .eq(Objects.nonNull(role), JobUser::getRole, role)
            .eq(JobUser::getDisabled, false)
            .eq(JobUser::getDeleted, false)
            .orderBy(true, false, JobUser::getId)
            .last("limit 1"))
        .asOpt();
  }

  public IPage<User> getJoinedWorkers(
      Long requestUid, Long jobId, Integer page, Integer limit, JobUserRole role) {
    Optional<Node> myNode = nodeMapper
        .selectOne(new QueryWrapper<Node>().lambda().eq(Node::getNodeManagerId, requestUid))
        .asOpt();
    if (myNode.isEmpty()) {
      throw ControllerUtils.badRequest("invalid data");
    }
    return userMapper.selectJoinPage(
        Page.of(page, limit),
        User.class,
        JoinWrappers.lambda(User.class)
            .selectAll(User.class)
            .leftJoin(JobUser.class, JobUser::getUserId, User::getId)
            .eq(JobUser::getTaskListSessionId, jobId)
            .eq(JobUser::getRole, role)
            .eq(JobUser::getDeleted, false)
            .eq(JobUser::getDisabled, false));
  }

  public List<ExamSession> getFirstUnReviewedJobUserExamSessions(Long jobId) {
    List<ExamSession> selectFirstExamSession = examSessionMapper.selectJoinList(
        ExamSession.class,
        JoinWrappers.lambda(ExamSession.class)
            .rightJoin(JobUser.class, JobUser::getId, ExamSession::getJobUserId)
            .selectAll(ExamSession.class)
            .eq(ExamSession::getJobId, jobId)
            .eq(JobUser::getRole, JobUserRole.POST_EXAM)
            .eq(ExamSession::getStatus, ExamSession.ExamSessionStatus.SUBMITTED)
            .eq(ExamSession::getReviewResult, "")
            .eq(JobUser::getDeleted, false)
            .eq(JobUser::getDisabled, false)
            .last(" limit 1")
        // .eq(ExamSession::getDeleted, false)
        );
    if (selectFirstExamSession.isEmpty()) {
      return selectFirstExamSession;
    } else {
      ExamSession example = selectFirstExamSession.first();
      return examSessionMapper.selectList(new QueryWrapper<ExamSession>()
          .lambda()
          .eq(ExamSession::getJobUserId, example.getJobUserId())
          .eq(ExamSession::getStatus, ExamSession.ExamSessionStatus.SUBMITTED)
          .eq(ExamSession::getReviewResult, ""));
    }
  }

  @Transactional
  public void signNda(JobUser ju, List<BatchNDA> ndas, Job.JobType jobType) {
    if (jobType.equals(Job.JobType.NORMAL)) {
      lambdaUpdate()
          .eq(JobUser::getId, ju.getId())
          .set(JobUser::getRole, JobUserRole.INVITATION)
          .set(JobUser::getUpdatedAt, DateUtils.now())
          .update();
    }

    ndas.forEach(nda -> {
      ndaSignRecordMapper.insert(NDASignRecord.builder()
          .ndaId(nda.getId())
          .batchId(nda.getBatchId())
          .userId(ju.getUserId())
          .jobId(ju.getTaskListSessionId())
          .jobUserId(ju.getId())
          .signType(NDASignRecord.NDASignType.JobUser)
          .build());
    });
  }

  public UserActivateJobRoleSignMessageDTO getUserActiveJobRoleSignMessage(
      UserDTO u, Long jobId, Long jobUserId) {
    var user = new ai.saharaa.dto.sign.data.User(u.getId(), u.getWalletAddress());
    UserActivateJobRole data = UserActivateJobRole.builder()
        .user(user)
        .jobId(new Uint64(jobId))
        .jobUserId(new Uint64(jobUserId))
        .build();

    return new UserActivateJobRoleSignMessageDTO(chainConfig.getEIP712Domain(), data);
  }

  public Boolean activateUserJobRole(Long jobUserId) {
    lambdaUpdate().eq(JobUser::getId, jobUserId).set(JobUser::getActive, true).update();
    return jobUserMapper
        .selectOne(new QueryWrapper<JobUser>().lambda().eq(JobUser::getId, jobUserId))
        .getActive();
  }

  public List<JobUser> getPreTaskExamExpiredJobUsers(Long jobId, Integer limit) {
    return this.jobUserMapper.selectJoinList(
        JobUser.class,
        JoinWrappers.lambda(JobUser.class)
            .leftJoin(Job.class, Job::getId, JobUser::getTaskListSessionId)
            .selectAll(JobUser.class)
            .eq(Job::getDeleted, false)
            .eq(JobUser::getDeleted, false)
            .eq(Job::getStatus, Job.JobStatus.PRE_TASK)
            .eq(!Objects.isNull(jobId), Job::getId, jobId)
            .in(
                JobUser::getRole,
                List.of(JobUserRole.PRE_EXAM, JobUserRole.NDA_SIGNING, JobUserRole.INVITATION))
            .lt(Job::getPreTaskDeadline, DateUtils.now())
            .last("limit ${limit}"));
  }

  public void reassigningUserRole(
      Long jobId, Long userId, JobUser.JobUserRole role, Long currentUserId) {
    this.jobDao
        .getJobById(jobId)
        .filter(j -> !j.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("job not found"))
        .filter(j -> Objects.equals(j.getStatus(), Job.JobStatus.WORKING)
            || Objects.equals(j.getStatus(), Job.JobStatus.AUDITING))
        .ensurePresent(() -> ControllerUtils.notFound("wrong job status"));

    this.jobUserDao
        .getJobUserByJobAndUserId(jobId, currentUserId)
        .asOpt()
        .filter(j -> !j.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("job user not found"))
        .filter(j -> Objects.equals(j.getRole(), JobUser.JobUserRole.ADMIN))
        .ensurePresent(() -> ControllerUtils.notFound("wrong job user role"));

    JobUser jobUser = this.jobUserDao
        .getJobUserByJobAndUserId(jobId, userId)
        .asOpt()
        .filter(j -> !j.getDeleted())
        .orThrow(() -> ControllerUtils.notFound("job user not found"))
        .filter(j -> Objects.equals(j.getRole(), LABELER)
            || Objects.equals(j.getRole(), SPOTTER)
            || Objects.equals(j.getRole(), REVIEWER))
        .orElseThrow(() -> ControllerUtils.notFound("wrong job user role"));

    jobUser.setRole(role);
    this.jobUserDao.updateJobUserById(jobUser);
  }

  public void changeRole(Long jobId, Long userId, JobUser.JobUserRole role) {
    this.jobDao
        .getJobById(jobId)
        .filter(j -> !j.getDeleted())
        .ensurePresent(() -> ControllerUtils.notFound("job not found"));

    var jobUser = this.jobUserDao.getJobUserByJobAndUserId(jobId, userId);
    if (Objects.isNull(jobUser)) {
      throw ControllerUtils.notFound("job user not found");
    }

    jobUser.setRole(role);
    jobUser.setUpdatedAt(DateUtils.now());
    jobUserDao.updateJobUserById(jobUser);
  }

  public IPage<JobUser> queryJobUsers(
      Integer page,
      Integer size,
      Long jobId,
      Optional<JobUser.JobUserRole> jobUserRole,
      Boolean includeDeleted,
      JobFields sortBy,
      Boolean sortDesc) {
    return jobUserMapper.selectPage(
        Page.of(page, size),
        new QueryWrapper<JobUser>()
            .lambda()
            .eq(JobUser::getTaskListSessionId, jobId)
            .eq(
                jobUserRole.isPresent(),
                JobUser::getRole,
                jobUserRole.orElse(JobUser.JobUserRole.LABELER))
            .eq(!includeDeleted, JobUser::getDeleted, false)
            .orderBy(sortBy == JobFields.ID, !sortDesc, JobUser::getId)
            .orderBy(sortBy == JobFields.CREATED_AT, !sortDesc, JobUser::getCreatedAt)
            .orderBy(sortBy == JobFields.UPDATED_AT, !sortDesc, JobUser::getUpdatedAt));
  }

  public List<JobUser> getActiveJobUsers(Long jobId) {
    return jobUserMapper.selectList(new QueryWrapper<JobUser>()
        .lambda()
        .eq(JobUser::getTaskListSessionId, jobId)
        .eq(JobUser::getActive, true)
        .eq(JobUser::getDeleted, false));
  }

  public void create(JobUser ju) {
    jobUserMapper.insert(ju);
  }

  public void deleteByJobId(Long jobId) {
    lambdaUpdate()
        .eq(JobUser::getTaskListSessionId, jobId)
        .set(JobUser::getDeleted, true)
        .set(JobUser::getUpdatedAt, DateUtils.now())
        .update();
  }

  public void deleteByJobIdAndUserId(Long jobId, Long userId) {
    this.jobUserMapper.update(
        null,
        new UpdateWrapper<JobUser>()
            .lambda()
            .eq(JobUser::getTaskListSessionId, jobId)
            .eq(JobUser::getUserId, userId)
            .set(JobUser::getDeleted, true)
            .set(JobUser::getUpdatedAt, DateUtils.now()));
  }

  public void skipNotice(Long jobUserId, Long curId) {
    var userId = jobUserMapper.selectById(jobUserId).getUserId();
    if (!Objects.equals(curId, userId)) {
      throw ControllerUtils.badRequest("jobUser is not correct.");
    }
    jobUserDao.setJobUserShowNoticeToFalse(jobUserId);
  }

  public IPage<User> getPlatformBanning(
      Integer page, Integer limit, Integer userId, String address) {
    return individualsDao.getPlatformBanList(page, limit, userId, address);
  }

  public IPage<User> getPermanentBanning(
      Integer page, Integer limit, Integer userId, String address) {
    return individualsDao.getPermanentBanning(page, limit, userId, address);
  }

  public IPage<JobUserDetailDTO> getInJobBanning(
      Integer page, Integer limit, Long jobId, Integer userId, String address) {
    return individualsDao.getInJobBanning(page, limit, jobId, userId, address);
  }

  public Boolean removeUserBanningInPlatform(Long userId) {
    individualsDao.removeUserBanningInPlatform(userId);
    return true;
  }

  public Boolean unbanInJob(Long userId, Long jobId) {
    individualsDao.unbanInJob(userId, jobId);
    return true;
  }

  private List<JobUser> getJobIdsByJobUserIds(Collection<Long> jobUserIds) {
    return jobUserMapper.selectList(new QueryWrapper<JobUser>()
        .lambda()
        .in(JobUser::getId, jobUserIds)
        .eq(JobUser::getDeleted, false));
  }

  private Map<Long, Batch> getJobIdToBatchMapByJobIds(
      List<JobUser> jobUserList, Collection<Long> jobIds) {
    var jobs = jobDao.getJobByIds(jobIds);
    var jobBatchIdMap = jobs.toMap(Job::getId, Job::getBatchId);
    var batchIds = jobs.map(Job::getBatchId).toSet();
    var batches = batchDao.getBatchByIds(batchIds);
    var batchIdToBatchMap = batches.toMap(Batch::getId, Function.identity());
    var jobIdToBatchMap = jobBatchIdMap
        .entrySet()
        .toMap(Map.Entry::getKey, entry -> batchIdToBatchMap.get(entry.getValue()));
    return jobUserList.toMap(
        JobUser::getId, jobUser -> jobIdToBatchMap.get(jobUser.getTaskListSessionId()));
  }

  public void disPointsInRewardPoolMode(Long jobId, Set<Long> resetUserIds) {
    log.info(
        "[SettlePreCalc -> distribution-start] triggered by job ${jobId} in reward pool type.");
    boolean taskShouldGoOn = true;
    var job = jobDao.getJobById(jobId).orElseThrow(() -> ControllerUtils.notFound("invalid data"));
    var batch = batchDao
        .getBatchById(job.getBatchId())
        .orElseThrow(() -> ControllerUtils.notFound("invalid data"));
    var taskRewardTokens = newTasksDao.listRewardTokenRecordsByBatchId(batch.getId());
    var mainTaskRewardToken = taskRewardTokens
        .sorted(Comparator.comparing(BatchRewardRecord::getAmount))
        .findFirst();
    if (mainTaskRewardToken.isEmpty()) {
      throw ControllerUtils.notFound("invalid data");
    }
    var completeCountForLabelingDistributing =
      String.format(TASK_TOKEN_DISTRIBUTION_COMPLETE_LABELING, jobId);
    if (!resetUserIds.isEmpty()) {
      doResetUserIds(jobId, resetUserIds);
      if (cache.hasKey(completeCountForLabelingDistributing)) {
        cache.del(completeCountForLabelingDistributing);
      }
    }
    var batchSetting = batchDao.getBatchSettingById(batch.getId());
    var annotatingCompleteCount = 0L;
    if (cache.hasKey(completeCountForLabelingDistributing)) {
      annotatingCompleteCount =
          Long.parseLong(cache.get(completeCountForLabelingDistributing).toString());
    } else {
      annotatingCompleteCount = taskSessionDao.countPendingSpotTaskSessionsByJobId(jobId);
      cache.set(completeCountForLabelingDistributing, annotatingCompleteCount, 5L * 60);
    }
    var maxCompleteCount =
        job.getAssignDataVolume() * batchSetting.getAnnotatingTimesAnnotationPerDatapoint();
    var distributingTotalTokenAmount = mainTaskRewardToken
        .get()
        .getAmount()
        .multiply(BigDecimal.valueOf(1.0f * annotatingCompleteCount / maxCompleteCount));
    var eachAnnotatingPoints = BigDecimal.ZERO;
    var eachReviewingPoints = BigDecimal.ZERO;
    var mathRoundPolicyForCalcFiles = new MathContext(28, RoundingMode.FLOOR);
    if (!batch.getReviewerRequired()) {
      eachAnnotatingPoints = distributingTotalTokenAmount.divide(
          BigDecimal.valueOf(annotatingCompleteCount), mathRoundPolicyForCalcFiles);
    } else {
      var completeCountForReviewingDistributing =
          String.format(TASK_TOKEN_DISTRIBUTION_COMPLETE_REVIEWING, jobId);
      var reviewingCompleteCount = 0L;
      if (cache.hasKey(completeCountForReviewingDistributing)) {
        reviewingCompleteCount =
            Long.parseLong(cache.get(completeCountForReviewingDistributing).toString());
      } else {
        reviewingCompleteCount = reviewSessionDao.countCompleteSessionCount(jobId);
        cache.set(completeCountForReviewingDistributing, reviewingCompleteCount, 5L * 60);
      }
      var ratioSum = batch
          .getReviewingPrice()
          .multiply(BigDecimal.valueOf(reviewingCompleteCount))
          .add(batch.getAnnotatingPrice().multiply(BigDecimal.valueOf(annotatingCompleteCount)))
          .round(mathRoundPolicyForCalcFiles);
      var priceCell = distributingTotalTokenAmount.divide(ratioSum, mathRoundPolicyForCalcFiles);
      eachAnnotatingPoints =
          batch.getAnnotatingPrice().multiply(priceCell).round(mathRoundPolicyForCalcFiles);
      eachReviewingPoints =
          batch.getReviewingPrice().multiply(priceCell).round(mathRoundPolicyForCalcFiles);
    }
    do {
      var waitingPointsDisJobUsers = jobUserDao.getWaitingPointsUsers(jobId, 300);
      if (waitingPointsDisJobUsers.isEmpty()) {
        taskShouldGoOn = false;
      } else {
        BigDecimal finalEachReviewingPoints = eachReviewingPoints;
        BigDecimal finalEachAnnotatingPoints = eachAnnotatingPoints;
        waitingPointsDisJobUsers.forEach(waitingJobUser -> {
          var sessionPrice = BigDecimal.ZERO;
          if (waitingJobUser.getRole().equals(LABELER)) sessionPrice = finalEachAnnotatingPoints;
          if (waitingJobUser.getRole().equals(REVIEWER)) sessionPrice = finalEachReviewingPoints;
          if (sessionPrice.compareTo(BigDecimal.ZERO) > 0) {
            jobUserPointPreCalcService.doDisPointInNewMode(
                waitingJobUser,
                batch,
                sessionPrice,
                batchSetting,
                mainTaskRewardToken.get(),
                taskRewardTokens);
          }
        });
      }

    } while (taskShouldGoOn);
  }

  @Transactional
  private void doResetUserIds(Long jobId, Set<Long> resetUserIds) {
    jobUserDao.resetTokenDisStatus(jobId, resetUserIds);
    newTasksDao.resetJobUserRecords(jobId, resetUserIds);
  }

  /**
   * Summary: jobId is null, means triggered by schedule won't calc bonus. is provided, means job
   * ended, do bonus.
   */
  public void doSettleAllPreCalcRecs(Timestamp standardTime, Long jobId) {
    Integer limit = 300;
    int sum = 0;
    boolean taskShouldGoOn = true;
    if (jobId == null) {
      log.info("[SettlePreCalc -> distribution-start] triggered by schedule.");
    } else {
      log.info("[SettlePreCalc -> distribution-start] triggered by job finished id is ${jobId}.");
    }
    do {
      var res = jobId == null
          ? jobUserPointPreCalcService.getPreCalcWithDdl(standardTime, limit)
          : jobUserPointPreCalcService.getPreCalcByJobIdWithDdl(jobId, limit);
      log.info(
          "[SettlePreCalc -> distribution-running] distribution running for jobId ${jobId}, ${res.size()} preCalcRecords fetched.");
      if (res.isEmpty()) {
        log.info(
            "[SettlePreCalc -> distribution-ending] for jobId ${jobId}, ${sum} jobUsers processed in total.");
        taskShouldGoOn = false;
        if (jobId != null) {
          if (!individualsDao.checkJobAlreadySentBonus(jobId)) {
            var j = jobDao.getJobById(jobId).get();
            var batch = batchDao.getBatchById(j.getBatchId()).get();
            jobUserPointPreCalcService.processSingleJobUserBonusPreJobUser(
                jobId, batch, DateUtils.now());
          }
        }
      }
      var jobUserList = getJobIdsByJobUserIds(
          res.map(JobUserPointsGroupedDataDTO::getJobUserId).toSet());
      var jobUserIdToBatchMap = getJobIdToBatchMapByJobIds(
          jobUserList, jobUserList.map(JobUser::getTaskListSessionId).toSet());
      var jobUsersMap = jobUserList.toMap(JobUser::getId, Function.identity());
      jobUserPointPreCalcService.settlePreCalcRecs(
          res, standardTime, jobUserIdToBatchMap, jobUsersMap);
      sum += res.size();
    } while (taskShouldGoOn);
  }

  public void recalculateCounters(Long jobId, Long userId) {
    var ju = jobUserMapper.selectOne(new QueryWrapper<JobUser>()
        .lambda()
        .eq(JobUser::getTaskListSessionId, jobId)
        .eq(JobUser::getUserId, userId)
        .eq(JobUser::getDeleted, false));
    if (ju == null) {
      throw ControllerUtils.badRequest("JobUser does not exist.");
    }
    if (ju.getRole() == LABELER) {
      long submittedCount = taskSessionMapper.selectCount(new QueryWrapper<TaskSession>()
          .lambda()
          .eq(TaskSession::getJobId, jobId)
          .eq(TaskSession::getUserId, userId)
          .ne(
              TaskSession::getStatus,
              TaskSession.TaskSessionStatus.PENDING) // Submitted means not pending
          .eq(TaskSession::getDeleted, false));
      commonCounterManager.setJobUserCount(
          jobId, userId, Constants.CACHE_KEY_FOR_JOB_USER.SUBMITTED_COUNT.value, submittedCount);
      long completeCount = taskSessionMapper.selectCount(new QueryWrapper<TaskSession>()
          .lambda()
          .eq(TaskSession::getJobId, jobId)
          .eq(TaskSession::getUserId, userId)
          .eq(TaskSession::getStatus, TaskSession.TaskSessionStatus.PendingSpot)
          .eq(TaskSession::getDeleted, false));
      commonCounterManager.setJobUserCount(
          jobId, userId, Constants.CACHE_KEY_FOR_JOB_USER.COMPLETE_COUNT.value, completeCount);
      commonCounterManager.setJobUserCount(
          jobId, userId, Constants.CACHE_KEY_FOR_JOB_USER.USER_RIGHT_COUNT.value, completeCount);
    } else if (ju.getRole() == REVIEWER) {
      long submittedCount = reviewSessionMapper.selectCount(new QueryWrapper<ReviewSession>()
          .lambda()
          .eq(ReviewSession::getJobId, jobId)
          .eq(ReviewSession::getUserId, userId)
          .ne(ReviewSession::getStatus, ReviewSession.ReviewSessionStatus.PENDING)
          .eq(ReviewSession::getDeleted, false));
      commonCounterManager.setJobUserCount(
          jobId, userId, Constants.CACHE_KEY_FOR_JOB_USER.SUBMITTED_COUNT.value, submittedCount);

      // Reviewer COMPLETE_COUNT
      long completeCount = reviewSessionMapper.selectCount(new QueryWrapper<ReviewSession>()
          .lambda()
          .eq(ReviewSession::getJobId, jobId)
          .eq(ReviewSession::getUserId, userId)
          .eq(ReviewSession::getStatus, ReviewSession.ReviewSessionStatus.SPOTTED)
          .eq(ReviewSession::getDeleted, false));
      commonCounterManager.setJobUserCount(
          jobId, userId, Constants.CACHE_KEY_FOR_JOB_USER.COMPLETE_COUNT.value, completeCount);

      // Reviewer USER_RIGHT_COUNT
      long rightCount = reviewSessionMapper.selectCount(new QueryWrapper<ReviewSession>()
          .lambda()
          .eq(ReviewSession::getJobId, jobId)
          .eq(ReviewSession::getUserId, userId)
          .eq(ReviewSession::getStatus, ReviewSession.ReviewSessionStatus.SPOTTED)
          .eq(ReviewSession::getWrongReview, false) // Key difference
          .eq(ReviewSession::getDeleted, false));
      commonCounterManager.setJobUserCount(
          jobId, userId, Constants.CACHE_KEY_FOR_JOB_USER.USER_RIGHT_COUNT.value, rightCount);
    }
  }

  @Transactional
  public void finalizeJobOnChain(Long jobId) {
    log.info("Starting on-chain finalization for job ID: {}", jobId);

    if (taskSessionDao.checkIfPendingPipelineSessionExist(jobId)) {
      throw new IllegalStateException("still some session pending pipeline.");
    }

    // 1. Fetch all rewards calculated for this job.
    List<UserTokenTaskRewards> rewardsForJob =
        userTokenTaskRewardsDao.findMainTokenRewardsByJobId(jobId);
    if (rewardsForJob.isEmpty()) {
      log.warn("No rewards to finalize for job ID: {}. Aborting.", jobId);
      return;
    }
    if (rewardsForJob.first().getMerkleProof() != null
        && !rewardsForJob.first().getMerkleProof().isEmpty()) {
      log.warn("No rewards to finalize for job ID: {}. Aborting.", jobId);
      throw new IllegalStateException("already done.");
    }

    // 2. Prepare RewardAllocation DTOs for MerkleTreeService.
    // This involves fetching user wallet addresses and token contract addresses.
    List<RewardAllocation> allocations = prepareRewardAllocations(rewardsForJob, jobId);
    if (allocations.isEmpty()) {
      log.error(
          "Failed to prepare reward allocations for job ID: {}. Critical data missing.", jobId);
      throw new IllegalStateException("Cannot finalize job due to missing user/token chain info.");
    }

    // 3. Call MerkleTreeService to build the tree and interact with the contract.
    try {
      merkleTreeService.finalizeTaskAndGenerateMerkleTree(jobId, allocations);
      log.info("Successfully submitted finalization for job ID: {} to the blockchain.", jobId);
      // The actual confirmation will come from the event listener.
      // Here, we can optimistically update the job status.
      // jobDao.updateStatus(jobId, JobStatus.PENDING_ON_CHAIN_CONFIRMATION);
      var job =
          jobDao.getJobById(jobId).orElseThrow(() -> ControllerUtils.notFound("invalid data"));
      var mainTokenRecord = batchRewardRecordDao
          .listByBatchId(job.getBatchId())
          .filter(r -> !r.getRewardTokenType().equals(RewardTokenType.THIRD_PARTY_TOKEN))
          .toList();
      mainTokenRecord.forEach(r -> r.setPoolStatus(BatchRewardRecord.PoolStatus.PREPARED));
      batchRewardRecordDao.updateAll(mainTokenRecord);
    } catch (Exception e) {
      log.error("On-chain finalization failed for job ID: {}", jobId, e);
      // Handle the exception, e.g., by setting job status to FAILED.
      throw new RuntimeException("On-chain finalization failed", e);
    }
  }

  private List<RewardAllocation> prepareRewardAllocations(
      List<UserTokenTaskRewards> rewards, Long jobId) {
    // 1. Collect all unique user IDs and token IDs to fetch related data in batches.
    List<Long> userIds = rewards.stream()
        .map(UserTokenTaskRewards::getUserId)
        .distinct()
        .collect(Collectors.toList());
    List<Integer> tokenInfoTypeIds = rewards.stream() // 3rd tokens are excluded.
        .map(r -> r.getRewardTokenType().getValue())
        .distinct()
        .collect(Collectors.toList());

    if (userIds.isEmpty() || tokenInfoTypeIds.isEmpty()) {
      return new ArrayList<>();
    }

    // 2. Fetch all necessary user and token info in single queries.
    Map<Long, User> userMap = userDao.findAllByIdIn(userIds).stream()
        .collect(Collectors.toMap(User::getId, Function.identity()));
    Map<Integer, RewardTokenInfo> tokenInfoMap =
        rewardTokenInfoDao.findAllByTypeIn(tokenInfoTypeIds).stream()
            .collect(Collectors.toMap(r -> r.getRewardTokenType().getValue(), Function.identity()));

    // 3. Build the final list of RewardAllocation DTOs.
    List<RewardAllocation> allocations = new ArrayList<>();
    for (UserTokenTaskRewards reward : rewards) {
      User user = userMap.get(reward.getUserId());
      RewardTokenInfo tokenInfo = tokenInfoMap.get(reward.getRewardTokenType().getValue());

      // 4. Critical data validation.
      if (user == null
          || user.getWalletAddress() == null
          || user.getWalletAddress().isEmpty()) {
        log.warn(
            "Skipping reward ID {} because user {} has no wallet address.",
            reward.getId(),
            reward.getUserId());
        continue;
      }
      if (tokenInfo == null
          || tokenInfo.getContractAddressBsc() == null
          || tokenInfo.getContractAddressBsc().isEmpty()) {
        log.warn(
            "Skipping reward ID {} because token type {} has no contract address.",
            reward.getId(),
            reward.getRewardTokenType().getValue());
        continue;
      }
      if (tokenInfo.getTokenDecimal() == null) {
        log.warn(
            "Skipping reward ID {} because token {} has no decimal information.",
            reward.getId(),
            reward.getThirdPartyTokenId());
        continue;
      }

      // 5. Convert amount from BigDecimal to BigInteger in the token's smallest unit.
      //      BigDecimal multiplier = BigDecimal.TEN.pow(tokenInfo.getTokenDecimal()); // already in
      // correct format
      //      BigInteger amountInSmallestUnit =
      // reward.getAmount().multiply(multiplier).toBigInteger();

      allocations.add(RewardAllocation.builder()
          .userId(reward.getUserId())
          .userAddress(user.getWalletAddress())
          .rewardTokenType(reward.getRewardTokenType().getValue())
          .tokenAddress(tokenInfo.getContractAddressBsc())
          .amount(reward.getAmount().toBigInteger())
          .tokenRewardId(reward.getId())
          .taskId(jobId)
          .build());
    }

    return allocations;
  }

  public void jobRewardPoolApproved(Long batchId) {
    batchRewardRecordDao.jobRewardPoolApproved(batchId);
  }

  public void updateTaskStatus(Long batchId, Integer taskStatus) {
    var target = batchRewardRecordDao.findMainTypesByBatchId(batchId);
    if (target.isEmpty()) {
      throw ControllerUtils.notFound("invalid data");
    }
    var newStatus = BatchRewardRecord.PoolStatus.DRAFT;
    if (Objects.equals(taskStatus, BatchRewardRecord.PoolStatus.LAUNCHED_WITH_CD.value)) {
      newStatus = BatchRewardRecord.PoolStatus.LAUNCHED_WITH_CD;
      var targetJobs = jobDao.listJobsByBatchId(batchId);
      if (targetJobs.isEmpty()) {
        throw ControllerUtils.notFound("no jobs");
      }
      var res = dspRewardContract
          .getTaskInfo(BigInteger.valueOf(targetJobs.first().getId()))
          .send();
      if (res.component2().isEmpty()) {
        throw ControllerUtils.notFound("task pool not created yet.");
      }
      var wrongStatusRec = target
          .filter(brr -> !brr.getPoolStatus().equals(BatchRewardRecord.PoolStatus.PREPARED))
          .findAny();
      if (wrongStatusRec.isPresent()) {
        throw ControllerUtils.notFound("invalid data");
      }
    }
    if (Objects.equals(taskStatus, BatchRewardRecord.PoolStatus.LAUNCHED_WITH_CD_READY.value)) {
      newStatus = BatchRewardRecord.PoolStatus.LAUNCHED_WITH_CD_READY;
      var wrongStatusRec = target
          .filter(brr -> !brr.getPoolStatus().equals(BatchRewardRecord.PoolStatus.LAUNCHED_WITH_CD))
          .findAny();
      if (wrongStatusRec.isPresent()) {
        throw ControllerUtils.notFound("invalid data");
      }
    }
    BatchRewardRecord.PoolStatus finalNewStatus = newStatus;
    target.forEach(r -> r.setPoolStatus(finalNewStatus));
    batchRewardRecordDao.updateAll(target);
  }
}
