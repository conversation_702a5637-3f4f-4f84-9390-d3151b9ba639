package ai.saharaa.services;

import ai.saharaa.actors.achievement.AchievementRouter;
import ai.saharaa.actors.achievement.AchievementTriggers;
import ai.saharaa.daos.*;
import ai.saharaa.daos.newTasks.NewTasksDao;
import ai.saharaa.daos.season.SeasonDao;
import ai.saharaa.daos.season.SeasonUserPointsDetailDao;
import ai.saharaa.dto.task.JobUserPointsGroupedDataDTO;
import ai.saharaa.dto.task.JobUserPointsGroupedForBonusDTO;
import ai.saharaa.mappers.JobUserPointsMapper;
import ai.saharaa.mappers.season.JobUserPointsPreCalcMapper;
import ai.saharaa.model.Batch;
import ai.saharaa.model.BatchSetting;
import ai.saharaa.model.JobUser;
import ai.saharaa.model.JobUserPoints;
import ai.saharaa.model.newTasks.BatchRewardRecord;
import ai.saharaa.model.season.JobUserPointsPreCalc;
import ai.saharaa.model.season.SeasonUserPointsDetail;
import ai.saharaa.services.season.rule.SeasonRuleMarch;
import ai.saharaa.utils.DateUtils;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import io.micrometer.core.instrument.MeterRegistry;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import lombok.Builder;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import manifold.rt.api.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

@Service
@Slf4j
public class JobUserPointPreCalcService
    extends ServiceImpl<JobUserPointsPreCalcMapper, JobUserPointsPreCalc> {
  private final String USER_DATA_POINTS_COUNTER = "user.points.data";
  private final String USER_BONUS_POINTS_COUNTER = "user.points.bonus";
  private final JobUserPointsPreCalcMapper jobUserPointsPreCalcMapper;
  private final MeterRegistry meterRegistry;
  private final JobUserPointsMapper jobUserPointsMapper;
  private final SeasonUserPointsDetailDao seasonUserPointsDetailDao;
  private final NotificationService notificationService;
  private final IndividualsDao individualsDao;
  private final JobUserPointsDao jobUserPointsDao;
  private final JobUserDao jobUserDao;
  private final TaskSessionDao taskSessionDao;
  private final ReviewSessionDao reviewSessionDao;
  private final SeasonDao seasonDao;
  private final AchievementRouter achievementRouter;
  private final NewTasksDao newTasksDao;

  public JobUserPointPreCalcService(
      JobUserPointsMapper jobUserPointsMapper,
      NotificationService notificationService,
      SeasonDao seasonDao,
      SeasonUserPointsDetailDao seasonUserPointsDetailDao,
      AchievementRouter achievementRouter,
      MeterRegistry meterRegistry,
      IndividualsDao individualsDao,
      JobUserPointsDao jobUserPointsDao,
      JobUserDao jobUserDao,
      TaskSessionDao taskSessionDao,
      ReviewSessionDao reviewSessionDao,
      NewTasksDao newTasksDao,
      JobUserPointsPreCalcMapper jobUserPointsPreCalcMapper) {
    this.jobUserPointsPreCalcMapper = jobUserPointsPreCalcMapper;
    this.notificationService = notificationService;
    this.seasonUserPointsDetailDao = seasonUserPointsDetailDao;
    this.jobUserPointsMapper = jobUserPointsMapper;
    this.achievementRouter = achievementRouter;
    this.reviewSessionDao = reviewSessionDao;
    this.jobUserPointsDao = jobUserPointsDao;
    this.taskSessionDao = taskSessionDao;
    this.individualsDao = individualsDao;
    this.meterRegistry = meterRegistry;
    this.jobUserDao = jobUserDao;
    this.newTasksDao = newTasksDao;
    this.seasonDao = seasonDao;
  }

  public Boolean settlePreCalcRecs(
      List<JobUserPointsGroupedDataDTO> list,
      Timestamp standardTime,
      Map<Long, Batch> jobUserIdToBatchMap,
      Map<Long, JobUser> jobUsersMap) {
    for (JobUserPointsGroupedDataDTO jobUserPointsPreCalc : list) {
      processSingleJobUserPreCalcRecord(
          jobUserPointsPreCalc,
          standardTime,
          jobUserIdToBatchMap.get(jobUserPointsPreCalc.getJobUserId()),
          jobUsersMap.get(jobUserPointsPreCalc.getJobUserId()));
    }
    return true;
  }

  @Transactional
  private void calculateNormalPoints(
      JobUserPointsGroupedDataDTO jobUserPointsGroupedDataDTO,
      Timestamp standardTime,
      Batch batch,
      JobUser jobUser) {
    jobUserPointsPreCalcMapper.update(
        null,
        new UpdateWrapper<JobUserPointsPreCalc>()
            .lambda()
            .eq(JobUserPointsPreCalc::getNotSettled, true)
            .eq(JobUserPointsPreCalc::getJobUserId, jobUserPointsGroupedDataDTO.getJobUserId())
            .lt(standardTime != null, JobUserPointsPreCalc::getFinalJudgeAt, standardTime)
            .set(JobUserPointsPreCalc::getNotSettled, false));
    var sessionPrice = jobUser.getRole().equals(JobUser.JobUserRole.LABELER)
        ? batch.getAnnotatingPrice()
        : batch.getReviewingPrice();
    var jobUserPointsObj = JobUserPoints.builder()
        .jobId(jobUser.getTaskListSessionId())
        .userId(jobUser.getUserId())
        .sessionCount(jobUserPointsGroupedDataDTO.getCount())
        .price(sessionPrice)
        .endCountAt(standardTime == null ? DateUtils.now() : standardTime)
        .rewardType(
            jobUser.getRole().equals(JobUser.JobUserRole.LABELER)
                ? JobUserPoints.JobUserPointsRewardType.ANNOTATE
                : JobUserPoints.JobUserPointsRewardType.REVIEW)
        .build();
    jobUserPointsMapper.insert(jobUserPointsObj);
    var points = BigDecimal.valueOf(
            jobUserPointsGroupedDataDTO.getCount() * sessionPrice.floatValue())
        .setScale(1, RoundingMode.FLOOR);
    meterRegistry.counter(USER_DATA_POINTS_COUNTER).increment(points.floatValue());

    var stat = JobUserStat.builder()
        .jup(jobUserPointsObj)
        .ju(jobUser)
        .basePoints(points)
        .build();
    appendPointsDetail(batch, stat);
    if (points.floatValue() > 0.0f) {
      notificationService.noticeUserGetSaharaPoint(
          jobUserPointsObj.getUserId(), points, jobUserPointsObj.getJobId());
    }
    log.info(
        "[SettlePreCalc -> dis-running -> rec] Appended annotate or review points detail: {}",
        stat);
  }

  private void calculateReviewerBonus(
      Long jobId,
      Timestamp standardTime,
      Batch batch,
      JobUser jobUser,
      double bonusAccuracy,
      Pair<Long, BigDecimal> userAllPointInJob,
      Integer bonusPercentage) {
    var approvedCount = reviewSessionDao.countUserApprovedSessionInJob(jobId, jobUser.getUserId());
    var wrongCount = reviewSessionDao.countUserWrongSessionInJob(jobId, jobUser.getUserId());
    var userAccuracy = (approvedCount * 1f) / (approvedCount + wrongCount);
    if (userAccuracy >= bonusAccuracy) {
      var extraBonusPointsCount = userAllPointInJob
          .getSecond()
          .multiply(BigDecimal.valueOf(bonusPercentage))
          .multiply(BigDecimal.valueOf(userAllPointInJob.getFirst()))
          .divide(BigDecimal.valueOf(10000));
      var bonusRec = JobUserPoints.builder()
          .sessionCount(extraBonusPointsCount.longValue())
          .userId(jobUser.getUserId())
          .rewardType(JobUserPoints.JobUserPointsRewardType.REVIEW_BONUS)
          .jobId(jobId)
          .endCountAt(standardTime)
          .price(batch.getReviewingPrice())
          .build();
      createJobUserPoints(bonusRec);
      var statReviewBonus = JobUserStat.builder()
          .jup(bonusRec)
          .ju(jobUser)
          .accuracy((long) Math.round(userAccuracy * 100))
          .accuracyCoefficient((long) bonusPercentage)
          .bonusPoints(extraBonusPointsCount)
          .revisedDataPoints(wrongCount)
          .build();
      appendPointsDetail(batch, statReviewBonus);
      if (bonusRec.getSessionCount() > 0) {
        notificationService.noticeUserFinishJob(
            bonusRec.getUserId(), bonusRec.getSessionCount().intValue(), bonusRec.getJobId());
      }
      meterRegistry
          .counter(USER_BONUS_POINTS_COUNTER)
          .increment(extraBonusPointsCount.floatValue());
    } else {
      recordEmptyBonusPoints(jobUser, standardTime);
    }
  }

  private void calculateLabelerBonus(
      Long jobId,
      Timestamp standardTime,
      Batch batch,
      JobUser jobUser,
      double bonusAccuracy,
      Pair<Long, BigDecimal> userAllPointInJob,
      Integer bonusPercentage) {
    var approvedCount = taskSessionDao.countUserApprovedSessionInJob(jobId, jobUser.getUserId());
    var revisedCount =
        taskSessionDao.countUserRejectedByMajorityVoteSessionsInJob(jobId, jobUser.getUserId());
    var userAccuracy = (1f * approvedCount) / (approvedCount + revisedCount);
    if (userAccuracy >= bonusAccuracy) {
      var extraBonusPointsCount = userAllPointInJob
          .getSecond()
          .multiply(BigDecimal.valueOf(bonusPercentage)
              .multiply(BigDecimal.valueOf(userAllPointInJob.getFirst()))
              .divide(BigDecimal.valueOf(10000.0f)));

      // Alert: for bonus, sessionCount means the bonus points count.
      var bonusRec = JobUserPoints.builder()
          .sessionCount(extraBonusPointsCount.longValue())
          .userId(jobUser.getUserId())
          .rewardType(JobUserPoints.JobUserPointsRewardType.ANNOTATE_BONUS)
          .jobId(jobId)
          .endCountAt(standardTime)
          .price(batch.getAnnotatingPrice())
          .build();
      createJobUserPoints(bonusRec);
      var statAnnotatorBonus = JobUserStat.builder()
          .jup(bonusRec)
          .ju(jobUser)
          .accuracy((long) Math.round(userAccuracy * 100))
          .accuracyCoefficient((long) bonusPercentage)
          .bonusPoints(extraBonusPointsCount)
          .revisedDataPoints(revisedCount)
          .build();
      appendPointsDetail(batch, statAnnotatorBonus);
      if (bonusRec.getSessionCount() > 0) {
        notificationService.noticeUserFinishJob(
            bonusRec.getUserId(), bonusRec.getSessionCount().intValue(), bonusRec.getJobId());
      }
      meterRegistry
          .counter(USER_BONUS_POINTS_COUNTER)
          .increment(extraBonusPointsCount.floatValue());
    } else {
      recordEmptyBonusPoints(jobUser, standardTime);
    }
  }

  @Transactional
  private void calculateBonusPoints(
      Timestamp standardTime, Batch batch, JobUser jobUser, Long jobId) {
    var bonusMinimumSubmissions = batch.getBonusMinimumSubmissions();
    var bonusPercentage = batch.getBonusPercentage();
    var bonusAccuracy = batch.getBonusRequiredAccuracy() / 10000.0;

    var userAllPointInJob = individualsDao.getUserAllPointsGotInJob(jobId, jobUser.getUserId());
    log.info(
        "[SettlePreCalc -> dis-running -> bonusing] userAllPointInJob: {}",
        userAllPointInJob.toString());
    if (userAllPointInJob.getFirst() >= bonusMinimumSubmissions) {
      if (jobUser.getRole().equals(JobUser.JobUserRole.LABELER)) {
        calculateLabelerBonus(
            jobId, standardTime, batch, jobUser, bonusAccuracy, userAllPointInJob, bonusPercentage);
      } else if (jobUser.getRole().equals(JobUser.JobUserRole.REVIEWER)) {
        calculateReviewerBonus(
            jobId, standardTime, batch, jobUser, bonusAccuracy, userAllPointInJob, bonusPercentage);
      }
    } else {
      recordEmptyBonusPoints(jobUser, standardTime);
    }
  }

  private void processSingleJobUserPreCalcRecord(
      JobUserPointsGroupedDataDTO jobUserPointsGroupedDataDTO,
      Timestamp standardTime,
      Batch batch,
      JobUser jobUser) {
    try {
      log.info(
          "[SettlePreCalc -> dis-running -> rec] for jobUserId ${jobUserPointsGroupedDataDTO.getJobUserId()}.");
      calculateNormalPoints(jobUserPointsGroupedDataDTO, standardTime, batch, jobUser);
    } catch (Exception e) {
      log.error(
          "[SettlePreCalc -> dis-running -> error] for preCalcRec jobUserId: ${jobUserPointsGroupedDataDTO.getJobUserId()}.",
          e);
    }
  }

  public void processSingleJobUserBonusPreJobUser(Long jobId, Batch batch, Timestamp standardTime) {
    if (batch.getBonusRequiredAccuracy() == null || batch.getBonusRequiredAccuracy() <= 0) {
      log.info(
          "[SettlePreCalc -> dis-running -> bonusing -> cancel] for jobId ${jobId} and bonus not enabled.");
      return;
    }
    try {
      var bonusTaskShouldGoOn = true;
      var offset = 0;
      var limit = 100;
      do {
        var jobUserPs = jobUserPointsDao.getJobUserPointsMapper(jobId, limit, offset);
        if (jobUserPs.isEmpty()) {
          log.info(
              "[SettlePreCalc -> dis-running -> bonusing -> end] for jobId ${jobId} and jobUser count is ${offset}.");
          bonusTaskShouldGoOn = false;
        } else {
          var jobUsers = jobUserDao.getJobUserByJobAndUserIds(
              jobId, jobUserPs.map(JobUserPointsGroupedForBonusDTO::getUserId).toSet());
          offset += jobUserPs.size();
          for (JobUser jobUser : jobUsers) {
            calculateBonusPoints(standardTime, batch, jobUser, jobId);
          }
          log.info(
              "[SettlePreCalc -> dis-running -> bonusing] for jobId ${jobId} and jobUser count is ${offset}.");
        }
      } while (bonusTaskShouldGoOn);
    } catch (Exception e) {
      log.error(
          "[SettlePreCalc -> dis-running -> bonusing -> error] for preCalcRec jobId: ${jobId}.", e);
    }
  }

  private void recordEmptyBonusPoints(JobUser jobUser, Timestamp nowTime) {
    log.info("[SettlePreCalc -> dis-running -> bonus] zero bonus finally for jobUser: {}", jobUser);
    var bonusRec = JobUserPoints.builder()
        .sessionCount(0L)
        .userId(jobUser.getUserId())
        .rewardType(
            jobUser.getRole().equals(JobUser.JobUserRole.LABELER)
                ? JobUserPoints.JobUserPointsRewardType.ANNOTATE_BONUS
                : JobUserPoints.JobUserPointsRewardType.REVIEW_BONUS)
        .jobId(jobUser.getTaskListSessionId())
        .endCountAt(nowTime)
        .price(BigDecimal.ZERO)
        .build();
    createJobUserPoints(bonusRec);
  }

  private void createJobUserPoints(JobUserPoints newRec) {
    individualsDao.createJobUserPoints(newRec);
    if (newRec.getSessionCount() > 0) {
      achievementRouter.trigger(new AchievementTriggers.JobUserPoints(newRec.getUserId()));
    }
  }

  public List<JobUserPointsGroupedDataDTO> getPreCalcWithDdl(Timestamp ddl, Integer limit) {
    return jobUserPointsPreCalcMapper.getPreCalcWithDdl(ddl, limit);
  }

  public List<JobUserPointsGroupedDataDTO> getPreCalcByJobIdWithDdl(Long jobId, Integer limit) {
    return jobUserPointsPreCalcMapper.getPreCalcByJobIdWithDdl(jobId, limit);
  }

  @Transactional
  public void doDisPointInNewMode(
      JobUser waitingJobUser,
      Batch batch,
      BigDecimal sessionPrice,
      BatchSetting batchSetting,
      BatchRewardRecord mainBatchRewardRecord,
      List<BatchRewardRecord> otherPartyTaskRewardTokens) {
    var finishedSessionCount = waitingJobUser.getRole().equals(JobUser.JobUserRole.LABELER)
        ? taskSessionDao.getFinishedSessionCount(waitingJobUser.getId())
        : reviewSessionDao.getFinishedSessionCount(waitingJobUser.getId());
    var baseTokenSendingAmount = sessionPrice.multiply(BigDecimal.valueOf(finishedSessionCount));

    newTasksDao.createUserTokenTaskRewardsRecord(
        mainBatchRewardRecord, waitingJobUser, baseTokenSendingAmount);
    for (BatchRewardRecord otherPartyTaskRewardToken : otherPartyTaskRewardTokens) {
      if (!otherPartyTaskRewardToken.getId().equals(mainBatchRewardRecord.getId())) {
        var targetPointsOf3rd = baseTokenSendingAmount
            .multiply(otherPartyTaskRewardToken
                .getAmount()
                .divide(mainBatchRewardRecord.getAmount(), 5, RoundingMode.FLOOR))
            .setScale(0, RoundingMode.FLOOR);
        newTasksDao.createUserTokenTaskRewardsRecord(
            otherPartyTaskRewardToken, waitingJobUser, targetPointsOf3rd);
      }
    }
    meterRegistry.counter(USER_DATA_POINTS_COUNTER).increment(baseTokenSendingAmount.floatValue());

    if (baseTokenSendingAmount.compareTo(BigDecimal.ONE) > 0) {
      //      notificationService.noticeUserGetSaharaPoint( // new notify
      //          jobUserPointsObj.getUserId(), targetPoints, jobUserPointsObj.getJobId());
    }
    waitingJobUser.setPointsDistributed(true);
    jobUserDao.updateJobUserById(waitingJobUser);
  }

  @Data
  @Builder
  private static class JobUserStat {
    JobUser ju;
    JobUserPoints jup;
    Long accuracy;
    Long accuracyCoefficient;
    BigDecimal basePoints;
    BigDecimal bonusPoints;
    Long revisedDataPoints;
  }

  private void appendPointsDetail(Batch batch, JobUserStat jus) {
    BigDecimal points =
        jus.getJup().getPrice().multiply(BigDecimal.valueOf(jus.getJup().getSessionCount()));
    if (points.floatValue() == 0f) {
      return;
    }

    var detailBuilder = SeasonRuleMarch.PointsDetail.builder()
        .taskName(batch.getName())
        .baned(jus.getJu().getDisabled())
        .role(jus.getJu().getRole())
        .labelType(batch.getLabelType())
        .accuracy(jus.getAccuracy())
        .accuracyCoefficient(jus.getAccuracyCoefficient())
        .revisedDataPoints(jus.revisedDataPoints)
        .dataPointPrice(jus.getJup().getPrice())
        .basePoints(jus.basePoints)
        .bonusPoints(jus.bonusPoints);

    if (jus.getJup().getRewardType().equals(JobUserPoints.JobUserPointsRewardType.ANNOTATE_BONUS)
        || jus.getJup()
            .getRewardType()
            .equals(JobUserPoints.JobUserPointsRewardType.REVIEW_BONUS)) {

      detailBuilder
          .bonusPoints(BigDecimal.valueOf(jus.getJup().getSessionCount()))
          .basePoints(BigDecimal.ZERO);
      points = BigDecimal.valueOf(jus.getJup().getSessionCount());
    } else if (JobUserPoints.JobUserPointsRewardType.POINT_AMPLIFIER.equals(
        jus.getJup().getRewardType())) {
      detailBuilder.amplifiedPoints(0L).bonusPoints(null).basePoints(BigDecimal.ZERO);
      points = BigDecimal.ZERO;
    } else {
      detailBuilder.dataPoints(jus.getJup().getSessionCount());
    }

    var detail = detailBuilder.build();
    var record = SeasonUserPointsDetail.builder()
        .seasonId(seasonDao.current().map((s) -> s.getId()).orElse(null))
        .userId(jus.getJup().getUserId())
        .jobId(jus.getJup().getJobId())
        .generationTime(jus.getJup().getEndCountAt())
        .points(points)
        .detail(detail)
        .build();
    seasonUserPointsDetailDao.insertUserPointsDetails(List.of(record));
    achievementRouter.trigger(
        new AchievementTriggers.UserPointsDetails(jus.getJup().getUserId()));
  }
}
