package ai.saharaa.services.achievement;

import static ai.saharaa.utils.Constants.*;
import static ai.saharaa.utils.OtherUtils.numberToRoman;

import ai.saharaa.actors.achievement.UserAchievementRecordActor;
import ai.saharaa.actors.achievement.UserSaharaLevelActor;
import ai.saharaa.common.cache.IGlobalCache;
import ai.saharaa.common.contracts.LevelAchievementManager;
import ai.saharaa.config.cluster.ClusterConfiguration;
import ai.saharaa.daos.SeasonAchievementDao;
import ai.saharaa.daos.UserDao;
import ai.saharaa.daos.achievement.*;
import ai.saharaa.daos.season.SeasonDao;
import ai.saharaa.daos.season.SeasonUserExpDetailDao;
import ai.saharaa.dto.achievement.*;
import ai.saharaa.dto.onchain.AchievementTokenMintedsDTO;
import ai.saharaa.dto.user.UserDTO;
import ai.saharaa.model.PageResult;
import ai.saharaa.model.SeasonAchievement;
import ai.saharaa.model.User;
import ai.saharaa.model.achievement.*;
import ai.saharaa.model.season.Season;
import ai.saharaa.model.season.SeasonUserExpDetail;
import ai.saharaa.services.NotificationService;
import ai.saharaa.utils.ActorUtils;
import ai.saharaa.utils.Constants;
import ai.saharaa.utils.ControllerUtils;
import ai.saharaa.utils.DateUtils;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.fasterxml.jackson.databind.ObjectMapper;
import java.io.IOException;
import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.time.Instant;
import java.time.ZoneId;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.web3j.protocol.Web3j;
import org.web3j.protocol.core.methods.response.TransactionReceipt;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;

@Service
@Slf4j
@RequiredArgsConstructor
public class AchievementService {
  private final AchievementDao achievementDao;
  private final AchievementLevelDao achievementLevelDao;
  private final SaharaLevelDao saharaLevelDao;
  private final UserSaharaLevelDao userSaharaLevelDao;
  private final UserAchievementRecordDao userAchievementRecordDao;
  private final AchievementRequirementDao achievementRequirementDao;
  private final AchievementOnContractDao achievementOnContractDao;
  private final UserDao userDao;
  private final UserAchievementDao userAchievementDao;
  private final SeasonAchievementDao seasonAchievementDao;
  private final SaharaLevelService saharaLevelService;
  private final NotificationService notificationService;
  private final LevelAchievementManager levelAchievementManager;
  private final Web3j web3j;
  private final ClusterConfiguration clusterConfiguration;
  private final IGlobalCache cache;
  private final SeasonUserExpDetailDao seasonUserExpDetailDao;
  private final SeasonDao seasonDao;
  private final UserAchievementProgressDao userAchievementProgressDao;

  @Value("${onchain.url.graphql}")
  private String graphqlUrl;

  public AchievementProfileDTO getAchievementProfile(Long userId) {
    var recordCount = userAchievementRecordDao.countRecordsByUserId(userId);
    var userSaharaLevel = userSaharaLevelDao.getByUserId(userId);

    var level = Objects.isNull(userSaharaLevel) ? 1 : userSaharaLevel.getLevel();
    var claimedLevel = Objects.isNull(userSaharaLevel) ? 1 : userSaharaLevel.getClaimedLevel();
    var onChainLevel = Objects.isNull(userSaharaLevel) ? 1 : userSaharaLevel.getOnChainLevel();
    var totalExp = Objects.isNull(userSaharaLevel) ? 0 : userSaharaLevel.getExp();
    var claimedExp = Objects.isNull(userSaharaLevel) ? 0 : userSaharaLevel.getClaimedExp();
    var onChainExp = Objects.isNull(userSaharaLevel) ? 0 : userSaharaLevel.getClaimedExp();
    var targetExp = saharaLevelDao.getTargetExpByLevel(level);
    var claimedTargetExp = saharaLevelDao.getTargetExpByLevel(claimedLevel);
    var onChainTargetExp = saharaLevelDao.getTargetExpByLevel(onChainLevel);
    return AchievementProfileDTO.builder()
        .level(level)
        .claimedLevel(claimedLevel)
        .onChainLevel(onChainLevel)
        .totalExp(totalExp)
        .claimedExp(claimedExp)
        .onChainExp(onChainExp)
        .targetExp(targetExp)
        .claimedTargetExp(claimedTargetExp)
        .onChainTargetExp(onChainTargetExp)
        .recordCount(recordCount)
        .build();
  }

  public AchievementDTO getAchievementInfoById(Long achievementId) {
    var key = Constants.getAchievementInfoIdKey(achievementId);
    var achievementDTO = (AchievementDTO) cache.get(key);
    if (Objects.nonNull(achievementDTO)) {
      return achievementDTO;
    }
    var achievementOpt = achievementDao.getById(achievementId);
    if (achievementOpt.isEmpty()) {
      return AchievementDTO.builder().build();
    }

    var levels = achievementLevelDao.getLevelsByAchievementId(achievementId);
    var requirements = achievementRequirementDao.getRequirementsByAchievementId(achievementId);
    achievementDTO = AchievementDTO.builder()
        .achievement(achievementOpt.get())
        .achievementLevels(levels)
        .achievementRequirements(requirements)
        .build();

    cache.set(key, achievementDTO, 10 * 60);
    return achievementDTO;
  }

  public AchievementDTO getAchievementInfoBySymbol(Achievement.Symbol symbol) {
    var key = Constants.getAchievementInfoSymbolKey(symbol.getValue());
    var achievementDTO = (AchievementDTO) cache.get(key);
    if (Objects.nonNull(achievementDTO)) {
      return achievementDTO;
    }
    var achievement = achievementDao.getBySymbol(symbol.getValue());
    if (Objects.isNull(achievement)) {
      return AchievementDTO.builder().build();
    }

    var levels = achievementLevelDao.getLevelsByAchievementId(achievement.getId());
    var requirements =
        achievementRequirementDao.getRequirementsByAchievementId(achievement.getId());
    achievementDTO = AchievementDTO.builder()
        .achievement(achievement)
        .achievementLevels(levels)
        .achievementRequirements(requirements)
        .build();

    cache.set(key, achievementDTO, 10 * 60);
    return achievementDTO;
  }

  public List<AchievementSeasonGroupDTO> getAchievementSeasonGroupBaseInfoInProgress() {
    var groups =
        (List<AchievementSeasonGroupDTO>) cache.get(ALL_ACHIEVEMENT_IN_PROGRESS_GROUP_BASIC_INFO);
    if (Objects.nonNull(groups)) {
      return groups;
    }

    var achievementDTOs = getAchievementDtoAll();
    groups = new ArrayList<>();

    var optSeason = seasonDao.current();
    if (optSeason.isPresent()) {
      var season = optSeason.get();

      var currentAchievementIds = seasonAchievementDao
          .getBySeasonId(season.getId())
          .map(SeasonAchievement::getAchievementId)
          .distinct()
          .toList();

      if (!CollectionUtils.isEmpty(currentAchievementIds)) {
        var currentDTOs = achievementDTOs
            .filter(a -> currentAchievementIds.contains(a.getAchievement().getId()))
            .toList();

        var seasonInfo = getSeasonInfoByAchievements(
            currentDTOs.map(AchievementDTO::getAchievement).toList(), season);
        var group = AchievementSeasonGroupDTO.builder()
            .seasonInfo(seasonInfo)
            .achievementDTOS(currentDTOs)
            .build();

        groups.add(group);
      }
    }

    var achievementIdsHasSeason = seasonAchievementDao.getAchievementIdsHasSeason();
    var dtosNoneSeason = achievementDTOs
        .filter(dto -> dto.getAchievement().getActive())
        .filter(dto -> !achievementIdsHasSeason.contains(dto.getAchievement().getId()))
        .toList();

    if (!CollectionUtils.isEmpty(dtosNoneSeason)) {
      var groupNoneSeason = AchievementSeasonGroupDTO.builder()
          .seasonInfo(getSeasonInfoByAchievements(
              dtosNoneSeason.map(AchievementDTO::getAchievement).toList(), null))
          .achievementDTOS(dtosNoneSeason)
          .build();
      groups.add(groupNoneSeason);
    }

    cache.set(ALL_ACHIEVEMENT_IN_PROGRESS_GROUP_BASIC_INFO, groups, 10 * 60);
    return groups;
  }

  public List<AchievementSeasonGroupDTO> getAchievementSeasonGroupBaseInfoClosed() {
    var groups =
        (List<AchievementSeasonGroupDTO>) cache.get(ALL_ACHIEVEMENT_CLOSED_GROUP_BASIC_INFO);
    if (Objects.nonNull(groups)) {
      return groups;
    }

    var achievementDTOs = getAchievementDtoAll();

    var idDTOMap = achievementDTOs.stream()
        .collect(Collectors.toMap(
            dto -> dto.getAchievement().getId(), dto -> dto, (dto0, dto1) -> dto0));

    var optSeason = seasonDao.current();
    var currentSeason = optSeason.orElse(null);
    var seasonAchievements = seasonAchievementDao.getNotEqSeasonId(
        Objects.isNull(currentSeason) ? null : currentSeason.getId());
    var seasonIds =
        seasonAchievements.map(SeasonAchievement::getSeasonId).distinct().toList();
    var seasons = seasonDao.getByIds(seasonIds);

    var achievementIdsHasSeason = seasonAchievementDao.getAchievementIdsHasSeason();

    var seasonIdAchievementsMap = seasonAchievements.collect(
        Collectors.groupingBy(SeasonAchievement::getSeasonId, Collectors.toList()));

    groups = Stream.concat(seasons.stream(), Stream.of((Season) null))
        .map(season -> {
          List<AchievementDTO> dtosOfThisSeason;
          if (Objects.isNull(season)) {
            dtosOfThisSeason = achievementDTOs
                .filter(dto ->
                    !achievementIdsHasSeason.contains(dto.getAchievement().getId()))
                .toList();
          } else {
            var achievementIdsOfThisSeason = seasonIdAchievementsMap
                .getOrDefault(season.getId(), List.of())
                .map(SeasonAchievement::getAchievementId)
                .sorted()
                .toList();
            dtosOfThisSeason = achievementIdsOfThisSeason
                .map(idDTOMap::get)
                .filter(Objects::nonNull)
                .toList();
          }

          var seasonInfo = getSeasonInfoByAchievements(
              dtosOfThisSeason.map(AchievementDTO::getAchievement).toList(), season);

          return AchievementSeasonGroupDTO.builder()
              .seasonInfo(seasonInfo)
              .achievementDTOS(dtosOfThisSeason)
              .build();
        })
        .filter(g -> !CollectionUtils.isEmpty(g.getAchievementDTOS()))
        .collect(Collectors.toList());
    cache.set(ALL_ACHIEVEMENT_CLOSED_GROUP_BASIC_INFO, groups, 10 * 60);
    return groups;
  }

  public void clearBaseInfoCache() {
    cache.del(
        ALL_ACHIEVEMENT_BASIC_INFO,
        ALL_ACHIEVEMENT_IN_PROGRESS_GROUP_BASIC_INFO,
        ALL_ACHIEVEMENT_CLOSED_GROUP_BASIC_INFO);
  }

  public Boolean isTimeActive(Achievement achievement) {
    var now = Instant.now().atZone(ZoneId.of("UTC")).toLocalDateTime();
    var startAt = achievement.getStartAt();
    var endAt = achievement.getEndAt();
    if (Objects.nonNull(endAt)) {
      var endDate = endAt.toInstant().plus(1, ChronoUnit.DAYS);
      endAt = Timestamp.from(endDate);
    }
    return (Objects.isNull(startAt) || now.isAfter(startAt.toLocalDateTime()))
        && (Objects.isNull(endAt) || now.isBefore(endAt.toLocalDateTime()));
  }

  private AchievementDTO getAchievementDtoOfUser(AchievementDTO dto, Long userId) {
    if (Objects.isNull(dto)
        || Objects.isNull(dto.getAchievement())
        || Objects.isNull(dto.getAchievement().getId())) {
      throw ControllerUtils.notFound("getAchievementDto empty achievement");
    }
    var achievementId = dto.getAchievement().getId();

    if (Objects.isNull(userId)) {
      return dto;
    }

    var userAchievement = userAchievementDao.getByAchievementIdAndUserId(achievementId, userId);

    dto.setUserAchievement(userAchievement);

    if (Achievement.LevelType.DAILY_TRICKLE.equals(dto.getAchievement().getLevelType())) {
      var checkedInCount = userAchievement.getRequirementBitmap().bitCount();
      var levels = dto.getAchievementLevels();
      levels.forEach(level -> {
        var leftDays = level.getDenominator().intValue() - checkedInCount;
        level.setRequirement(
            level.getRequirement().replaceAll("\\$\\{days}", String.valueOf(leftDays)));
      });
    }
    return dto;
  }

  public List<AchievementDTO> getAchievementDtosOfUser(List<AchievementDTO> dtos, Long userId) {
    if (Objects.isNull(userId) || CollectionUtils.isEmpty(dtos)) {
      return dtos;
    }
    if (dtos.map(AchievementDTO::getAchievement)
        .anyMatch(a -> Objects.isNull(a) || Objects.isNull(a.getId()))) {
      throw ControllerUtils.notFound("getAchievementDto empty achievement");
    }
    var achievementIds = dtos.map(AchievementDTO::getAchievement)
        .map(Achievement::getId)
        .distinct()
        .toList();
    if (CollectionUtils.isEmpty(achievementIds)) {
      return dtos;
    }

    var userAchievements = userAchievementDao.getByAchievementIdsAndUserId(achievementIds, userId);

    var uaMap = userAchievements.stream()
        .collect(
            Collectors.toMap(UserAchievement::getAchievementId, (ua) -> ua, (ua0, ua1) -> ua0));

    dtos.forEach(dto -> {
      var achievementId = dto.getAchievement().getId();
      var ua = uaMap.get(achievementId);
      if (Objects.isNull(ua)) {
        ua = UserAchievement.builder()
            .achievementId(achievementId)
            .userId(userId)
            .level(1)
            .onChainLevel(1)
            .unclaimedExp(0L)
            .passed(false)
            .onChainPassed(false)
            .requirementBitmap(BigInteger.ZERO)
            .onChainRequirementBitmap(BigInteger.ZERO)
            .progressMap(Map.of())
            .progress(0L)
            .build();
      }
      dto.setUserAchievement(ua);
    });
    return dtos;
  }

  public AchievementDTO getAchievementDtoById(Long achievementId, Long userId) {
    var dto = getAchievementInfoById(achievementId);
    return getAchievementDtoOfUser(dto, userId);
  }

  public AchievementDTO getAchievementDtoBySymbol(Achievement.Symbol symbol, Long userId) {
    var dto = getAchievementInfoBySymbol(symbol);
    return getAchievementDtoOfUser(dto, userId);
  }

  public Achievement getAchievementBySymbol(Achievement.Symbol symbol) {
    return achievementDao.getBySymbol(symbol.getValue());
  }

  public List<AchievementDTO> getAchievementDtoList(Long userId) {
    var achievements = getAchievementAll();
    return achievements
        .map(a -> {
          var dto = getAchievementInfoById(a.getId());
          return getAchievementDtoOfUser(dto, userId);
        })
        .toList();
  }

  public List<AchievementDTO> getAchievementDtoAll() {
    var achievements = getAchievementAll();
    return achievements.map(a -> getAchievementInfoById(a.getId())).toList();
  }

  public List<Achievement> getAchievementAll() {
    List<Achievement> achievements = (List<Achievement>) cache.get(ALL_ACHIEVEMENT_BASIC_INFO);
    if (Objects.nonNull(achievements)) {
      return achievements;
    }
    achievements = achievementDao.getAllAchievements();
    cache.set(ALL_ACHIEVEMENT_BASIC_INFO, achievements, 10 * 60);
    return achievements;
  }

  public AchievementTimesDTO getAchievementTimesByCategory(Achievement.Category category) {
    var achievements = getAchievementAll();
    var acs = achievements.filter(a -> category.equals(a.getCategory())).toList();

    var starts = acs.map(Achievement::getStartAt).filter(Objects::nonNull).toList();
    var claimStarts =
        acs.map(Achievement::getClaimStartAt).filter(Objects::nonNull).toList();
    var mintStarts =
        acs.map(Achievement::getMintStartAt).filter(Objects::nonNull).toList();

    var ends = acs.map(Achievement::getEndAt).filter(Objects::nonNull).toList();
    var claimEnds = acs.map(Achievement::getClaimEndAt).filter(Objects::nonNull).toList();
    var mintEnds = acs.map(Achievement::getMintEndAt).filter(Objects::nonNull).toList();

    return AchievementTimesDTO.builder()
        .category(category)
        .startAt(CollectionUtils.isEmpty(starts) ? null : Collections.min(starts))
        .endAt(CollectionUtils.isEmpty(ends) ? null : Collections.max(ends))
        .claimStartAt(CollectionUtils.isEmpty(claimStarts) ? null : Collections.min(claimStarts))
        .claimEndAt(CollectionUtils.isEmpty(claimEnds) ? null : Collections.max(claimEnds))
        .mintStartAt(CollectionUtils.isEmpty(mintStarts) ? null : Collections.min(mintStarts))
        .mintEndAt(CollectionUtils.isEmpty(mintEnds) ? null : Collections.max(mintEnds))
        .build();
  }

  public AchievementSeasonGroupDTO.SeasonInfo getSeasonInfoByAchievements(
      List<Achievement> acs, Season season) {
    var starts = acs.map(Achievement::getStartAt).filter(Objects::nonNull).toList();
    var claimStarts =
        acs.map(Achievement::getClaimStartAt).filter(Objects::nonNull).toList();
    var mintStarts =
        acs.map(Achievement::getMintStartAt).filter(Objects::nonNull).toList();

    var ends = acs.map(Achievement::getEndAt).filter(Objects::nonNull).toList();
    var claimEnds = acs.map(Achievement::getClaimEndAt).filter(Objects::nonNull).toList();
    var mintEnds = acs.map(Achievement::getMintEndAt).filter(Objects::nonNull).toList();

    return AchievementSeasonGroupDTO.SeasonInfo.builder()
        .startAt(CollectionUtils.isEmpty(starts) ? null : Collections.min(starts))
        .endAt(CollectionUtils.isEmpty(ends) ? null : Collections.max(ends))
        .claimStartAt(CollectionUtils.isEmpty(claimStarts) ? null : Collections.min(claimStarts))
        .claimEndAt(CollectionUtils.isEmpty(claimEnds) ? null : Collections.max(claimEnds))
        .mintStartAt(CollectionUtils.isEmpty(mintStarts) ? null : Collections.min(mintStarts))
        .mintEndAt(CollectionUtils.isEmpty(mintEnds) ? null : Collections.max(mintEnds))
        .seasonId(Objects.isNull(season) ? null : season.getId())
        .seasonName(Objects.isNull(season) ? null : season.getAchievementTitle())
        .seasonType(Objects.isNull(season) ? null : season.getRuleVersion())
        .seasonStartAt(Objects.isNull(season) ? null : season.getStartedAt())
        .seasonEndAt(Objects.isNull(season) ? null : season.getEndedAt())
        .build();
  }

  public PageResult<AchievementExpRecordDTO> getAchievementExpRecords(
      Long userId, Integer page, Integer size) {
    var rPage = userAchievementRecordDao.getRecordPage(userId, page, size);
    var achievementIds = rPage
        .getRecords()
        .map(UserAchievementRecord::getAchievementId)
        .distinct()
        .toList();
    var achievementIdNameMap = CollectionUtils.isEmpty(achievementIds)
        ? Map.<Long, String>of()
        : achievementDao.getByIds(achievementIds).toMap(Achievement::getId, Achievement::getName);
    var achievementLevelIds = rPage
        .getRecords()
        .map(UserAchievementRecord::getAchievementId)
        .distinct()
        .toList();
    var achievementLevelIdLevelMap = CollectionUtils.isEmpty(achievementLevelIds)
        ? Map.<Long, Integer>of()
        : achievementLevelDao
            .getByIds(achievementLevelIds)
            .toMap(AchievementLevel::getId, AchievementLevel::getLevel);

    return PageResult.<AchievementExpRecordDTO>builder()
        .pages(rPage.getPages())
        .size(rPage.getSize())
        .current(rPage.getCurrent())
        .total(rPage.getTotal())
        .data(rPage
            .getRecords()
            .map(r -> AchievementExpRecordDTO.builder()
                .id(r.getId())
                .date(r.getClaimedAt())
                .achievement(achievementIdNameMap.getOrDefault(r.getAchievementId(), ""))
                .exp(r.getIncreasedExp())
                .level(achievementLevelIdLevelMap.getOrDefault(r.getAchievementLevelId(), 1))
                .build())
            .toList())
        .build();
  }

  public Boolean isPassed(Achievement.Symbol symbol, Long curId) {
    return passedStatus(symbol, curId).getT1();
  }

  public Boolean isOnchainPassed(Achievement.Symbol symbol, Long curId) {
    if (symbol.equals(Achievement.Symbol.DEMI_GOD)) {
      if (cache.hasKey(getDemiGodUserCacheKey(curId))) {
        return Boolean.valueOf(cache.get(getDemiGodUserCacheKey(curId)).toString());
      }
      var curUser = userDao.getUserById(curId);
      if (!curUser.isEmpty()) {
        var currentSeason = seasonDao.current();
        if (currentSeason.isPresent()) {
          var targetAchievementOnContract = achievementOnContractDao.getAchievementOnContracts(
              null, null, DEMI_GOD_SYMBOL_IN_LATEST_SEASON, null);
          if (!targetAchievementOnContract.isEmpty()
              && targetAchievementOnContract.anyMatch(a ->
                  queryAchievementNFT(curUser.get().getWalletAddress(), a.getAchievementId()))) {
            cache.set(getDemiGodUserCacheKey(curId), Boolean.TRUE, 15 * 60L);
            return true;
          }
        }
      }
      var oldVersionValue = passedStatus(symbol, curId).getT2();
      cache.set(getDemiGodUserCacheKey(curId), oldVersionValue, 15 * 60L);
      return oldVersionValue;
    }
    return passedStatus(symbol, curId).getT2();
  }

  public Tuple2<Boolean, Boolean> passedStatus(Achievement.Symbol symbol, Long curId) {
    var dto = getAchievementInfoBySymbol(symbol);
    var achievementId = dto.getAchievement().getId();
    var userAchievement = userAchievementDao.getByAchievementIdAndUserId(achievementId, curId);
    var passed = userAchievement.getPassed();

    var isPassed =
        switch (dto.getAchievement().getLevelType()) {
          case DISPOSABLE, DISPOSABLE_HIDDEN, DISPOSABLE_FAKE_LEVEL, UPGRADABLE -> passed;
          case DISPOSABLE_ACTIVE_FINAL, DAILY_TRICKLE -> passed
              && (Objects.isNull(dto.getAchievement().getMintStartAt())
                  || Instant.now()
                      .atZone(ZoneId.of("UTC"))
                      .toLocalDateTime()
                      .isAfter(dto.getAchievement().getMintStartAt().toLocalDateTime()));
        };
    var isOnChainPassed = userAchievement.getOnChainPassed();
    return Tuples.of(isPassed, isOnChainPassed);
  }

  private Boolean shouldReward(Achievement achievement, Boolean isUpgradable) {
    if (Objects.isNull(achievement) || !Boolean.TRUE.equals(achievement.getActive())) {
      return false;
    }
    var timeActive = isTimeActive(achievement);
    if (!timeActive) {
      return false;
    }

    var levelType = achievement.getLevelType();

    var upgradableLevelTypes = List.of(Achievement.LevelType.UPGRADABLE);
    var disposableLevelTypes = List.of(
        Achievement.LevelType.DISPOSABLE,
        Achievement.LevelType.DISPOSABLE_HIDDEN,
        Achievement.LevelType.DISPOSABLE_FAKE_LEVEL,
        Achievement.LevelType.DISPOSABLE_ACTIVE_FINAL,
        Achievement.LevelType.DAILY_TRICKLE);

    if (isUpgradable) {
      return upgradableLevelTypes.contains(levelType);
    } else {
      return disposableLevelTypes.contains(levelType);
    }
  }

  public Boolean rewardAchievement(
      Achievement.Symbol symbol, Long userId, Map<String, BigDecimal> progressDict) {
    return rewardAchievement(symbol, userId, progressDict, false, false, false);
  }

  public Boolean rewardAchievement(
      Achievement.Symbol symbol,
      Long userId,
      Map<String, BigDecimal> progressDict,
      boolean partProgress,
      boolean incrementalProgress) {
    return rewardAchievement(
        symbol, userId, progressDict, partProgress, incrementalProgress, false);
  }

  public Boolean rewardAchievement(
      Achievement.Symbol symbol,
      Long userId,
      Map<String, BigDecimal> progressDict,
      boolean partProgress,
      boolean incrementalProgress,
      boolean skipShouldVerify) {
    var achievementDto = getAchievementDtoBySymbol(symbol, userId);
    var achievement = achievementDto.getAchievement();
    if (!skipShouldVerify && !shouldReward(achievement, false)) {
      return false;
    }

    var userAchievement = achievementDto.getUserAchievement();
    var levelType = achievement.getLevelType();

    var firstLevel = achievementDto
        .getAchievementLevels()
        .filter(al -> al.getLevel() == 1)
        .findFirst()
        .orElse(null);
    if (Objects.isNull(firstLevel)) {
      return false;
    }

    var previousRequirementBitmap = userAchievement.getRequirementBitmap();

    var requirements = achievementDto
        .getAchievementRequirements()
        .sorted(Comparator.comparingInt(AchievementRequirement::getSort))
        .toList();

    var requirementIdRequirementMap =
        requirements.collect(Collectors.toMap(AchievementRequirement::getId, r -> r));

    var hasMultiProgress = achievementDto.hasMultiProgress();

    Map<String, Long> previousProgressMap;

    if (hasMultiProgress) {
      previousProgressMap = userAchievement.getProgressMap();

      // Load from userAchievementProgress
      if (CollectionUtils.isEmpty(previousProgressMap)
          && Objects.nonNull(userAchievement.getId())) {
        var oldProgresses =
            userAchievementProgressDao.getByAchievementIdAndUserId(achievement.getId(), userId);
        previousProgressMap = oldProgresses
            .filter((p) -> Objects.nonNull(p.getAchievementRequirementId()))
            .map((p) -> Map.entry(
                requirementIdRequirementMap.get(p.getAchievementRequirementId()).getBehaviorCode(),
                p.getProgress()))
            .toMap(Map.Entry::getKey, Map.Entry::getValue);
      }
    } else {
      previousProgressMap = requirements
          .map((r) -> {
            var passed = previousRequirementBitmap.testBit(r.getSort() - 1);
            return Map.entry(r.getBehaviorCode(), passed ? 1L : 0L);
          })
          .toMap(Map.Entry::getKey, Map.Entry::getValue);
    }

    Map<String, Long> pMap;
    if (partProgress) {
      pMap = Stream.concat(
              previousProgressMap.entrySet().stream(), progressDict.entrySet().stream())
          .collect(Collectors.toMap(
              Map.Entry::getKey,
              e -> e.getValue().longValue(),
              (v1, v2) -> incrementalProgress ? (v1 + v2) : v2));
    } else {
      pMap = incrementalProgress
          ? Stream.concat(previousProgressMap.entrySet().stream(), progressDict.entrySet().stream())
              .collect(
                  Collectors.toMap(Map.Entry::getKey, e -> e.getValue().longValue(), Long::sum))
          : progressDict.entrySet().toMap(Map.Entry::getKey, e -> e.getValue().longValue());
    }

    var passedRequirements = requirements
        .filter((r) -> pMap.getOrDefault(r.getBehaviorCode(), 0L) >= r.getDenominator())
        .toList();

    var passedRequirementCount = (long) passedRequirements.size();

    var allPassed = passedRequirementCount >= requirements.count();

    var anyPassed = passedRequirementCount > 0L;

    var requirementBitMap = BigInteger.valueOf(previousRequirementBitmap.longValue());
    var newPassedRequirementIds = new HashSet<Long>();

    for (AchievementRequirement r : passedRequirements) {
      var no = r.getSort() - 1;
      requirementBitMap = requirementBitMap.setBit(no);
      if (!previousRequirementBitmap.testBit(no)) {
        newPassedRequirementIds.add(r.getId());
      }
    }

    var userSaharaLevel = userSaharaLevelDao.getByUserId(userId);
    var previousExp = Objects.isNull(userSaharaLevel) ? 0L : userSaharaLevel.getExp();
    var acquiredExp = 0L;

    for (Long newPassedRequirementId : newPassedRequirementIds) {
      var requirement = requirementIdRequirementMap.get(newPassedRequirementId);
      if (requirement.getExp() > 0) {
        ActorUtils.askWithDefault(
                clusterConfiguration.getUserAchievementRecordActor(),
                new UserAchievementRecordActor.UserAchievementRecordCreate(
                    UserAchievementRecord.builder()
                        .userId(userId)
                        .achievementId(achievement.getId())
                        .achievementRequirementId(newPassedRequirementId)
                        .completedAt(DateUtils.now())
                        .previousExp(previousExp + (acquiredExp += requirement.getExp()))
                        .increasedExp(requirement.getExp())
                        .build()))
            .get();
      }
    }

    var achievementLevel = levelType == Achievement.LevelType.DISPOSABLE_FAKE_LEVEL
        ? achievementDto
            .getAchievementLevels()
            .sorted(Comparator.comparingInt(AchievementLevel::getLevel))
            .toList()
            .lastOrNull()
        : firstLevel;

    var progressMap = hasMultiProgress
        ? requirements
            .map((r) -> Map.entry(
                r.getBehaviorCode(),
                Long.min(r.getDenominator(), pMap.getOrDefault(r.getBehaviorCode(), 0L))))
            .toMap(Map.Entry::getKey, Map.Entry::getValue)
        : null;

    switch (levelType) {
      case DISPOSABLE, DISPOSABLE_HIDDEN -> userAchievementDao.updateLevel(
          userId,
          achievement.getId(),
          1,
          allPassed,
          null,
          null,
          allPassed ? achievementLevel.getExp() : 0L,
          requirementBitMap,
          progressMap,
          passedRequirementCount);
      case DISPOSABLE_FAKE_LEVEL -> userAchievementDao.updateLevel(
          userId,
          achievement.getId(),
          (int) Math.min(passedRequirementCount + 1, firstLevel.getDenominator()),
          anyPassed,
          null,
          null,
          (allPassed ? achievementLevel.getExp() : 0L) + acquiredExp,
          requirementBitMap,
          progressMap,
          passedRequirementCount);
      case DISPOSABLE_ACTIVE_FINAL, DAILY_TRICKLE -> userAchievementDao.updateLevel(
          userId,
          achievement.getId(),
          1,
          anyPassed,
          null,
          null,
          (allPassed ? achievementLevel.getExp() : 0L) + acquiredExp,
          requirementBitMap,
          progressMap,
          passedRequirementCount);
      default -> {}
    }

    if (!newPassedRequirementIds.isEmpty()) {
      var withExp = achievementDto
          .getAchievementRequirements()
          .filter(r -> newPassedRequirementIds.contains(r.getId()))
          .anyMatch(r -> r.getExp() > 0L);
      notificationService.noticeUserHasCompleteAchievement(userId, achievement.getName(), withExp);
    }

    if (allPassed) {
      if (achievementLevel.getExp() > 0) {
        ActorUtils.askWithDefault(
                clusterConfiguration.getUserAchievementRecordActor(),
                new UserAchievementRecordActor.UserAchievementRecordCreate(
                    UserAchievementRecord.builder()
                        .userId(userId)
                        .achievementId(achievement.getId())
                        .achievementLevelId(achievementLevel.getId())
                        .completedAt(DateUtils.now())
                        .previousExp(previousExp + acquiredExp)
                        .increasedExp(achievementLevel.getExp())
                        .build()))
            .get();
      }
      if (acquiredExp + achievementLevel.getExp() > 0) {
        ActorUtils.askWithDefault(
                clusterConfiguration.getUserSaharaLevelActor(),
                new UserSaharaLevelActor.UserSaharaLevelUp(
                    userId, acquiredExp + achievementLevel.getExp()))
            .get();
      }
      // saharaLevelService.rewardExp(userId, levelDto.getAchievementLevel().getExp());
      return true;
    }
    return false;
  }

  public Boolean rewardAchievement(Achievement.Symbol symbol, Long userId, Long progress) {
    var achievementDto = getAchievementDtoBySymbol(symbol, userId);
    var achievement = achievementDto.getAchievement();
    if (!shouldReward(achievement, true)) {
      return false;
    }

    var userAchievement = achievementDto.getUserAchievement();

    if (Objects.nonNull(userAchievement) && userAchievement.getPassed()) {
      return false;
    }

    var passedMaxLevel = achievementDto
        .getAchievementLevels()
        .sorted((l1, l2) -> l2.getLevel() - l1.getLevel())
        .filter(l -> progress >= l.getDenominator())
        .findFirst()
        .orElse(null);
    var level = Objects.isNull(passedMaxLevel) ? 1 : passedMaxLevel.getLevel() + 1;

    var oLevel = userAchievement.getLevel();
    if (Objects.isNull(oLevel) || level < oLevel) {
      return false;
    }
    var userSaharaLevel = userSaharaLevelDao.getByUserId(userId);
    var levelMap = achievementDto.getAchievementLevels().toMap(AchievementLevel::getLevel);
    var maxLevel = achievementDto.getAchievementLevels().stream()
        .max(Comparator.comparingInt(AchievementLevel::getLevel))
        .get();

    var exp = 0L;
    var userExp = Objects.isNull(userSaharaLevel) ? 0L : userSaharaLevel.getExp();
    for (int l = oLevel; l <= level; l++) {
      var achievementLevel = levelMap.get(l);
      if (Objects.nonNull(achievementLevel)) {
        var passed = progress >= achievementLevel.getDenominator();
        if (passed) {
          ActorUtils.askWithDefault(
                  clusterConfiguration.getUserAchievementRecordActor(),
                  new UserAchievementRecordActor.UserAchievementRecordCreate(
                      UserAchievementRecord.builder()
                          .userId(userId)
                          .achievementId(achievement.getId())
                          .achievementLevelId(achievementLevel.getId())
                          .completedAt(DateUtils.now())
                          .previousExp(userExp + exp)
                          .increasedExp(achievementLevel.getExp())
                          .build()))
              .get();
          exp += achievementLevel.getExp();
        }
      }
    }
    var allPassed = level > maxLevel.getLevel() || progress >= maxLevel.getDenominator();
    if (allPassed) {
      var withExp = maxLevel.getExp() > 0L;
      notificationService.noticeUserHasCompleteAchievement(userId, achievement.getName(), withExp);
    }
    userAchievementDao.updateLevel(
        userId,
        achievement.getId(),
        allPassed ? maxLevel.getLevel() : level,
        allPassed,
        null,
        null,
        exp,
        null,
        null,
        progress);
    if (exp > 0) {
      ActorUtils.askWithDefault(
              clusterConfiguration.getUserSaharaLevelActor(),
              new UserSaharaLevelActor.UserSaharaLevelUp(userId, exp))
          .get();
    }
    // saharaLevelService.rewardExp(userId, exp);
    return true;
  }

  public Boolean offChainVerify(Long achievementId, Integer level, Long exp, UserDTO user) {
    if (Objects.isNull(achievementId)
        || Objects.isNull(level)
        || Objects.isNull(exp)
        || Objects.isNull(user)) {
      throw ControllerUtils.badRequest("parameters can't be null");
    }
    var achievementDto = getAchievementDtoById(achievementId, user.getId());
    var achievement = achievementDto.getAchievement();
    if (!Boolean.TRUE.equals(achievement.getActive())) {
      throw ControllerUtils.badRequest("achievement not active");
    }
    var userAchievement = achievementDto.getUserAchievement();
    if (Objects.isNull(userAchievement)) {
      throw ControllerUtils.badRequest("not eligible to claim");
    }
    var levelMap = achievementDto.getAchievementLevels().toMap(AchievementLevel::getLevel);
    var maxLevel = achievementDto
        .getAchievementLevels()
        .map(AchievementLevel::getLevel)
        .max(Comparator.comparingInt(l -> l))
        .get();
    var achievementLevel = levelMap.get(level);
    if (Objects.isNull(achievementLevel)) {
      throw ControllerUtils.badRequest("illegal level");
    }
    if (!Objects.equals(level, userAchievement.getOnChainLevel())) {
      throw ControllerUtils.badRequest("incorrect level");
    }
    if (!Objects.equals(achievementLevel.getExp(), exp)) {
      throw ControllerUtils.badRequest("incorrect exp");
    }
    // TODO syncAchievementClaimedStatus0(userAchievement, levelMap, maxLevel);
    if (!(userAchievement.getLevel() > level
        || (Objects.equals(userAchievement.getLevel(), maxLevel)
            && Objects.equals(level, maxLevel)
            && level.equals(maxLevel)
            && userAchievement.getPassed()
            && !userAchievement.getOnChainPassed()))) {
      throw ControllerUtils.badRequest("not eligible to claim");
    }

    if (achievement.hasRequirementExp()) {
      var requirementBitmap = userAchievement.getRequirementBitmap();
      var onChainRequirementBitmap = userAchievement.getOnChainRequirementBitmap();
      if (onChainRequirementBitmap.longValue() == 0L) {
        throw ControllerUtils.badRequest("at least 1 requirement passed");
      }
      var now = Instant.now().atZone(ZoneId.of("UTC")).toLocalDateTime();
      var claimStartAt = achievement.getClaimStartAt();
      var claimEndAt = achievement.getClaimEndAt();
      var timeCanClaim =
          (Objects.isNull(claimStartAt) || now.isAfter(claimStartAt.toLocalDateTime()))
              && (Objects.isNull(claimEndAt) || now.isBefore(claimEndAt.toLocalDateTime()));
      if (timeCanClaim && requirementBitmap.compareTo(onChainRequirementBitmap) != 0) {
        throw ControllerUtils.badRequest("claim all passed requirements first");
      }

      var mintStartAt = achievement.getMintStartAt();
      var mintEndAt = achievement.getMintEndAt();
      if (Achievement.LevelType.DAILY_TRICKLE.equals(achievement.getLevelType())
          && !onChainRequirementBitmap.testBit(achievementLevel.getDenominator().intValue() - 1)) {
        mintStartAt = achievement.getEndAt();
      }

      var timeCanMint = (Objects.isNull(mintStartAt) || now.isAfter(mintStartAt.toLocalDateTime()))
          && (Objects.isNull(mintEndAt) || now.isBefore(mintEndAt.toLocalDateTime()));
      if (!timeCanMint) {
        throw ControllerUtils.badRequest(
            "this achievement not in mint time ${mintStartAt} - ${mintEndAt}");
      }
    }
    return true;
  }

  public Boolean onChainSuccess(
      Long achievementId, Integer level, Long exp, Long userId, String contractAddress) {
    if (Objects.isNull(achievementId)
        || Objects.isNull(level)
        || Objects.isNull(exp)
        || Objects.isNull(userId)
        || Objects.isNull(contractAddress)) {
      throw ControllerUtils.badRequest("parameters can't be null");
    }
    var achievementDto = getAchievementDtoById(achievementId, userId);
    var userAchievement = achievementDto.getUserAchievement();
    if (Objects.isNull(userAchievement)) {
      log.info(
          "onChainSuccess none userAchievement {}, {}, {}, {}, {}",
          achievementId,
          level,
          exp,
          userId,
          contractAddress);
      return false;
    }
    var levelMap = achievementDto.getAchievementLevels().toMap(AchievementLevel::getLevel);
    var maxLevel = achievementDto
        .getAchievementLevels()
        .map(AchievementLevel::getLevel)
        .max(Comparator.comparingInt(l -> l))
        .get();
    var achievementLevel = levelMap.get(level);
    if (Objects.isNull(achievementLevel)) {
      log.info(
          "onChainSuccess none achievementLevel {}, {}, {}, {}, {}",
          achievementId,
          level,
          exp,
          userId,
          contractAddress);
      return false;
    }
    return onChainSuccess0(
        achievementId, level, exp, userId, contractAddress, achievementLevel, maxLevel);
  }

  public Boolean onChainSuccess0(
      Long achievementId,
      Integer level,
      Long exp,
      Long userId,
      String contractAddress,
      AchievementLevel achievementLevel,
      Integer maxLevel) {
    var isMaxLevel = level >= maxLevel;
    var i = userAchievementDao.updateOnChainSuccess(
        userId, achievementId, isMaxLevel ? maxLevel : level + 1, isMaxLevel, exp, contractAddress);
    if (i > 0) {
      var userSaharaLevel = saharaLevelService.rewardClaimedExp(userId, achievementLevel.getExp());
      userAchievementRecordDao.claimedByAchievementAndLevel(
          userId,
          achievementId,
          achievementLevel.getId(),
          null,
          Objects.isNull(userSaharaLevel) ? 0L : userSaharaLevel.getClaimedExp(),
          achievementLevel.getExp());
    }
    return i > 0;
  }

  public Integer fetchUserAchievementLevel(String walletAddress, Long achievementId) {
    var retryTimes = 6;
    for (int i = 0; i < retryTimes; i++) {
      try {
        var levelAndTokenId = levelAchievementManager
            .getUserAchievementLevelAndTokenId(BigInteger.valueOf(achievementId), walletAddress)
            .send();
        BigInteger level = levelAndTokenId.component1();
        return level.intValueExact();
      } catch (Exception e) {
        log.error(
            "fetchUserAchievementLevel from chain exception retrying : {}, {}",
            walletAddress,
            achievementId,
            e);
        Thread.sleep(2000);
      }
    }
    throw ControllerUtils.notFound("user achievement level not found on chain");
  }

  public Boolean syncAchievementClaimedStatus(Long achievementId, Long userId) {
    var achievementDto = getAchievementDtoById(achievementId, userId);
    var userAchievement = achievementDto.getUserAchievement();
    if (Objects.isNull(userAchievement)) {
      throw ControllerUtils.badRequest("not eligible to claim");
    }
    var levelMap = achievementDto.getAchievementLevels().toMap(AchievementLevel::getLevel);
    var maxLevel = achievementDto
        .getAchievementLevels()
        .map(AchievementLevel::getLevel)
        .max(Comparator.comparingInt(l -> l))
        .get();

    return syncAchievementClaimedStatus0(userAchievement, levelMap, maxLevel);
  }

  public Boolean syncAchievementClaimedStatus0(
      UserAchievement userAchievement, Map<Integer, AchievementLevel> levelMap, Integer maxLevel) {
    var canClaim = userAchievement.getLevel() > userAchievement.getOnChainLevel()
        || (Objects.equals(userAchievement.getLevel(), maxLevel)
            && Objects.equals(userAchievement.getOnChainLevel(), maxLevel)
            && userAchievement.getPassed()
            && !userAchievement.getOnChainPassed());
    log.info("syncAchievementClaimedStatus : {}, canClaim : {}", userAchievement, canClaim);
    if (!canClaim) {
      throw ControllerUtils.badRequest("can not mint");
    }
    var user = userDao
        .getUserById(userAchievement.getUserId())
        .orElseThrow(() -> ControllerUtils.notFound("user not found"));
    var level =
        fetchUserAchievementLevel(user.getWalletAddress(), userAchievement.getAchievementId());
    log.info("syncAchievementClaimedStatus : {}, onChainLevel : {}", userAchievement, level);
    var achievementLevel = levelMap.get(level);
    if (Objects.isNull(achievementLevel) || level < userAchievement.getOnChainLevel()) {
      throw ControllerUtils.badRequest("illegal level");
    }

    return onChainSuccess0(
        userAchievement.getAchievementId(),
        level,
        achievementLevel.getExp(),
        user.getId(),
        "",
        achievementLevel,
        maxLevel);
  }

  public List<UserAchievement> getUserAchievementsCanClaim() {
    return userAchievementDao.getUserAchievementsCanClaim();
  }

  public List<BadgeDTO> getBadgeDtoList(Long userId) {
    if (Objects.isNull(userId)) {
      return List.of();
    }
    var achievements = getAchievementAll();
    var achievementIdMap = achievements.toMap(Achievement::getId);
    var userAchievements = userAchievementDao.getByUserId(userId);
    return userAchievements
        .filter(ua -> achievementIdMap.containsKey(ua.getAchievementId()))
        .filter(ua -> ua.getOnChainLevel() > 1 || ua.getOnChainPassed())
        .filter(ua -> achievementIdMap.get(ua.getAchievementId()).getLevelType()
                != Achievement.LevelType.DISPOSABLE_FAKE_LEVEL
            || ua.getOnChainPassed())
        .map(ua -> {
          var achievement = achievementIdMap.get(ua.getAchievementId());
          var onChainLevel =
              ua.getOnChainPassed() ? ua.getOnChainLevel() : (ua.getOnChainLevel() - 1);
          var achievementLevel = achievementLevelDao.getLevelByAchievementIdAndLevel(
              ua.getAchievementId(),
              achievement.getLevelType() == Achievement.LevelType.DISPOSABLE_ACTIVE_FINAL
                  ? ua.getOnChainRequirementBitmap().bitCount()
                  : onChainLevel);
          return BadgeDTO.builder()
              .achievementId(achievement.getId())
              .logo(Objects.isNull(achievementLevel) ? "" : achievementLevel.getLogo())
              .name(String.format("%s %s", achievement.getName(), numberToRoman(onChainLevel)))
              .level(Objects.isNull(achievementLevel) ? 1 : onChainLevel)
              .build();
        })
        .toList();
  }

  public Boolean requirementExpVerify(
      Long achievementId, Long requirementId, Long exp, Long userId) {
    var achievementDto = getAchievementDtoById(achievementId, userId);
    var achievement = achievementDto.getAchievement();
    var userAchievement = achievementDto.getUserAchievement();
    if (Objects.isNull(userAchievement)) {
      return false;
    }
    if (!Boolean.TRUE.equals(achievement.getActive())) {
      throw ControllerUtils.badRequest("achievement not active");
    }
    if (!achievement.hasRequirementExp()) {
      throw ControllerUtils.badRequest("achievement has no requirement exp");
    }

    var now = Instant.now().atZone(ZoneId.of("UTC")).toLocalDateTime();
    var claimStartAt = achievement.getClaimStartAt();
    var claimEndAt = achievement.getClaimEndAt();
    var timeCanClaim = (Objects.isNull(claimStartAt) || now.isAfter(claimStartAt.toLocalDateTime()))
        && (Objects.isNull(claimEndAt) || now.isBefore(claimEndAt.toLocalDateTime()));
    if (!timeCanClaim) {
      throw ControllerUtils.badRequest(
          "this achievement not in claim time ${claimStartAt} - ${claimEndAt}");
    }

    var requirement = achievementDto
        .getAchievementRequirements()
        .filter(r -> Objects.equals(r.getId(), requirementId))
        .findFirst()
        .orElseThrow(() -> ControllerUtils.notFound("requirement not found"));
    var requirementBitmap = userAchievement.getRequirementBitmap();
    var onChainRequirementBitmap = userAchievement.getOnChainRequirementBitmap();
    var no = requirement.getSort() - 1;
    if (!requirementBitmap.testBit(no)) {
      throw ControllerUtils.badRequest("requirement not passed");
    }
    if (onChainRequirementBitmap.testBit(no)) {
      throw ControllerUtils.badRequest("requirement already claimed");
    }
    return true;
  }

  public Boolean requirementExpSuccess(
      Long achievementId, Long requirementId, Long exp, User user) {
    var achievementDto = getAchievementDtoById(achievementId, user.getId());
    var achievement = achievementDto.getAchievement();
    log.info("onchain-levelup-success ${user.getId()} achievement 806 ${achievement}");
    var requirement = achievementDto
        .getAchievementRequirements()
        .filter(ar -> ar.getId().equals(requirementId))
        .findFirst()
        .orElseThrow(() -> ControllerUtils.notFound("requirement not found"));

    var previousOnChainRequirementBitmap =
        achievementDto.getUserAchievement().getOnChainRequirementBitmap();
    var newlySuccess = !previousOnChainRequirementBitmap.testBit(requirement.getSort() - 1);
    var onChainRequirementBitmap =
        previousOnChainRequirementBitmap.setBit(requirement.getSort() - 1);
    var onChainRequirementBitmapValue = onChainRequirementBitmap.longValue();

    var firstLevel = achievementDto.getAchievementLevels().firstOrNull();
    var denominator =
        Objects.isNull(firstLevel) ? 1 : firstLevel.getDenominator().intValue();
    var fullPassedD = (1 << denominator) - 1;
    var allOnChainPassed = (onChainRequirementBitmapValue & fullPassedD) == fullPassedD;
    var levelType = achievement.getLevelType();

    var requirementExpPassed = onChainRequirementBitmapValue > 0 && allOnChainPassed;

    var fakeOnChainLevel = onChainRequirementBitmap.bitCount() + 1;
    log.info("onchain-levelup-success ${user.getId()} fakeOnChainLevel ${fakeOnChainLevel}");

    userAchievementDao.updateOnChainClaimExpSuccess(
        user.getId(),
        achievementId,
        onChainRequirementBitmapValue,
        levelType == Achievement.LevelType.DISPOSABLE_FAKE_LEVEL
            ? Math.min(fakeOnChainLevel, denominator)
            : null,
        achievement.hasRequirementExp() ? requirementExpPassed : null);
    var userSaharaLevel = saharaLevelService.getByUserId(user.getId());
    log.info("onchain-levelup-success ${user.getId()} fakeOnChainLevel ${fakeOnChainLevel}");
    userAchievementRecordDao.claimedByAchievementAndLevel(
        user.getId(),
        achievementId,
        null,
        requirement.getId(),
        Objects.isNull(userSaharaLevel) ? 0L : userSaharaLevel.getClaimedExp(),
        requirement.getExp());
    log.info("onchain-levelup-success ${user.getId()} fakeOnChainLevel ${fakeOnChainLevel}");

    log.info("onchain-levelup-success ${user.getId()} updateProgress");
    if (newlySuccess) {
      saharaLevelService.rewardClaimedExp(user.getId(), requirement.getExp());
    }

    var optSeason = seasonDao.current();
    log.info("onchain-levelup-success ${optSeason} optSeason");
    if (optSeason.isPresent()) {
      log.info("onchain-levelup-success ${optSeason} optSeason");
      var currentSeasonId = optSeason.get().getId();
      var seasonAchievements = seasonAchievementDao.getByAchievementId(achievementId);
      var seasonUserExpDetails = seasonAchievements
          .map(SeasonAchievement::getSeasonId)
          .filter(seasonId -> Objects.equals(currentSeasonId, seasonId))
          .map(seasonId -> SeasonUserExpDetail.builder()
              .userId(user.getId())
              .seasonId(seasonId)
              .achievementId(achievementId)
              .exp(requirement.getExp())
              .detail(SeasonUserExpDetail.RequirementDetail.builder()
                  .requirementBehaviorCode(requirement.getBehaviorCode())
                  .build())
              .build())
          .toList();
      log.info("onchain-levelup-success seasonUserExpDetails ${seasonUserExpDetails} ");
      seasonUserExpDetailDao.insertUserExpDetails(seasonUserExpDetails);
    }

    return true;
  }

  public Achievement createAchievement(Achievement achievement) {
    achievement = achievementDao.create(achievement);
    cache.del(ALL_ACHIEVEMENT_BASIC_INFO);
    return achievement;
  }

  public Optional<Achievement> getAchievementById(Long id) {
    return achievementDao.getById(id);
  }

  public void updateAchievement(Long id, AchievementCreateUpdateDTO dto, Achievement achievement) {
    var update = new UpdateWrapper<Achievement>()
        .lambda()
        .eq(Achievement::getId, id)
        .set(Achievement::getUpdatedAt, DateUtils.now());

    dto.getSymbol()
        .asOpt()
        .filter(symbol -> !symbol.equals(achievement.getSymbol()))
        .ifPresent(symbol -> {
          throw ControllerUtils.badRequest(
              "Uneditable symbol ${achievement.getSymbol()} to ${symbol}");
        });
    dto.getName().asOpt().ifPresent(name -> update.set(Achievement::getName, name));
    dto.getDescription()
        .asOpt()
        .ifPresent(description -> update.set(Achievement::getDescription, description));
    dto.getCategory().asOpt().ifPresent(category -> update.set(Achievement::getCategory, category));
    dto.getLevelType()
        .asOpt()
        .filter(levelType -> !levelType.equals(achievement.getLevelType()))
        .ifPresent(levelType -> {
          throw ControllerUtils.badRequest(
              "Uneditable levelType ${achievement.getLevelType()} to ${levelType}");
        });
    dto.getStandard().asOpt().ifPresent(standard -> update.set(Achievement::getStandard, standard));
    dto.getStandardUri()
        .asOpt()
        .ifPresent(standardUri -> update.set(Achievement::getStandardUri, standardUri));
    dto.getNetwork().asOpt().ifPresent(network -> update.set(Achievement::getNetwork, network));
    dto.getFeature().asOpt().ifPresent(feature -> update.set(Achievement::getFeature, feature));
    dto.getSort().asOpt().ifPresent(sort -> update.set(Achievement::getSort, sort));
    dto.getActive().asOpt().ifPresent(active -> update.set(Achievement::getActive, active));
    dto.getStartAt().asOpt().ifPresent(startAt -> update.set(Achievement::getStartAt, startAt));
    dto.getEndAt().asOpt().ifPresent(endAt -> update.set(Achievement::getEndAt, endAt));
    dto.getClaimStartAt()
        .asOpt()
        .ifPresent(mintStartAt -> update.set(Achievement::getClaimStartAt, mintStartAt));
    dto.getClaimEndAt()
        .asOpt()
        .ifPresent(mintEndAt -> update.set(Achievement::getClaimEndAt, mintEndAt));
    dto.getMintStartAt()
        .asOpt()
        .ifPresent(mintStartAt -> update.set(Achievement::getMintStartAt, mintStartAt));
    dto.getMintEndAt()
        .asOpt()
        .ifPresent(mintEndAt -> update.set(Achievement::getMintEndAt, mintEndAt));

    achievementDao.update(null, update);
    cache.del(Constants.getAchievementInfoIdKey(id));
    cache.del(ALL_ACHIEVEMENT_BASIC_INFO);
  }

  public void deleteAchievementById(Long id) {
    achievementDao.deleteById(id);
    cache.del(Constants.getAchievementInfoIdKey(id));
    cache.del(ALL_ACHIEVEMENT_BASIC_INFO);
  }

  public AchievementDTO createAchievementLevel(AchievementLevel achievementLevel) {
    achievementLevelDao.create(achievementLevel);
    cache.del(Constants.getAchievementInfoIdKey(achievementLevel.getAchievementId()));
    return getAchievementInfoById(achievementLevel.getAchievementId());
  }

  public void updateAchievementLevel(
      Long id, AchievementLevel newAchievementLevel, AchievementLevel achievementLevel) {
    var update = new UpdateWrapper<AchievementLevel>()
        .lambda()
        .eq(AchievementLevel::getId, id)
        .set(AchievementLevel::getUpdatedAt, DateUtils.now());

    newAchievementLevel
        .getAchievementId()
        .asOpt()
        .filter(achievementId -> !achievementId.equals(achievementLevel.getAchievementId()))
        .ifPresent(achievementId -> {
          throw ControllerUtils.badRequest(
              "Uneditable achievementId ${achievementId} of ${achievementLevel}");
        });
    newAchievementLevel
        .getLevel()
        .asOpt()
        .filter(level -> !level.equals(achievementLevel.getLevel()))
        .ifPresent(level -> {
          throw ControllerUtils.badRequest("Uneditable level ${level} of ${achievementLevel}");
        });
    newAchievementLevel
        .getExp()
        .asOpt()
        .ifPresent(exp -> update.set(AchievementLevel::getExp, exp));
    newAchievementLevel
        .getBehaviorCode()
        .asOpt()
        .ifPresent(behaviorCode -> update.set(AchievementLevel::getBehaviorCode, behaviorCode));
    newAchievementLevel
        .getRequirement()
        .asOpt()
        .ifPresent(requirement -> update.set(AchievementLevel::getRequirement, requirement));
    newAchievementLevel
        .getDenominator()
        .asOpt()
        .ifPresent(denominator -> update.set(AchievementLevel::getDenominator, denominator));
    newAchievementLevel
        .getLogo()
        .asOpt()
        .ifPresent(logo -> update.set(AchievementLevel::getLogo, logo));

    achievementLevelDao.update(null, update);
    cache.del(Constants.getAchievementInfoIdKey(id));
  }

  public void deleteAchievementLevel(AchievementLevel achievementLevel) {
    achievementLevelDao.deleteById(achievementLevel.getId());
    cache.del(Constants.getAchievementInfoIdKey(achievementLevel.getAchievementId()));
  }

  public AchievementDTO createAchievementRequirement(
      AchievementRequirement achievementRequirement) {
    achievementRequirementDao.create(achievementRequirement);
    cache.del(Constants.getAchievementInfoIdKey(achievementRequirement.getAchievementId()));
    return getAchievementInfoById(achievementRequirement.getAchievementId());
  }

  public void updateAchievementRequirement(
      Long requirementId,
      AchievementRequirement newAchievementRequirement,
      AchievementRequirement achievementRequirement) {
    var update = new UpdateWrapper<AchievementRequirement>()
        .lambda()
        .eq(AchievementRequirement::getId, requirementId)
        .set(AchievementRequirement::getUpdatedAt, DateUtils.now());

    newAchievementRequirement
        .getAchievementId()
        .asOpt()
        .filter(achievementId -> !achievementId.equals(achievementRequirement.getAchievementId()))
        .ifPresent(achievementId -> {
          throw ControllerUtils.badRequest(
              "Uneditable achievementId ${achievementId} of ${achievementRequirement}");
        });
    newAchievementRequirement
        .getAchievementLevelId()
        .asOpt()
        .filter(levelId -> !levelId.equals(achievementRequirement.getAchievementLevelId()))
        .ifPresent(levelId -> {
          throw ControllerUtils.badRequest(
              "Uneditable levelId ${levelId} of ${achievementRequirement}");
        });
    newAchievementRequirement
        .getRequirement()
        .asOpt()
        .ifPresent(requirement -> update.set(AchievementRequirement::getRequirement, requirement));
    newAchievementRequirement
        .getDenominator()
        .asOpt()
        .ifPresent(denominator -> update.set(AchievementRequirement::getDenominator, denominator));
    newAchievementRequirement
        .getBehaviorCode()
        .asOpt()
        .ifPresent(
            behaviorCode -> update.set(AchievementRequirement::getBehaviorCode, behaviorCode));
    newAchievementRequirement
        .getName()
        .asOpt()
        .ifPresent(name -> update.set(AchievementRequirement::getName, name));
    newAchievementRequirement
        .getDescription()
        .asOpt()
        .ifPresent(description -> update.set(AchievementRequirement::getDescription, description));
    newAchievementRequirement
        .getLogo()
        .asOpt()
        .ifPresent(logo -> update.set(AchievementRequirement::getLogo, logo));
    newAchievementRequirement
        .getActionText()
        .asOpt()
        .ifPresent(actionText -> update.set(AchievementRequirement::getActionText, actionText));
    newAchievementRequirement
        .getActionPassedText()
        .asOpt()
        .ifPresent(actionPassedText ->
            update.set(AchievementRequirement::getActionPassedText, actionPassedText));
    newAchievementRequirement
        .getActionUri()
        .asOpt()
        .ifPresent(actionUri -> update.set(AchievementRequirement::getActionUri, actionUri));
    newAchievementRequirement
        .getSort()
        .asOpt()
        .ifPresent(sort -> update.set(AchievementRequirement::getSort, sort));

    achievementRequirementDao.update(null, update);
    cache.del(Constants.getAchievementInfoIdKey(achievementRequirement.getAchievementId()));
  }

  public void deleteAchievementRequirement(AchievementRequirement achievementRequirement) {
    achievementRequirementDao.deleteById(achievementRequirement.getId());
    cache.del(Constants.getAchievementInfoIdKey(achievementRequirement.getAchievementId()));
  }

  public void prepareClaimExp(
      Long achievementId, Long requirementId, Long userId, Integer level, Long exp) {
    cache.set(
        Constants.getAchievementRequirementExpKey(userId),
        PreparedClaimExpDTO.builder()
            .achievementId(achievementId)
            .requirementId(requirementId)
            .level(level)
            .exp(exp)
            .build(),
        5 * 60);
  }

  public void clearPreparedClaimExp(User user) {
    cache.del(Constants.getAchievementRequirementExpKey(user.getId()));
  }

  public PreparedClaimExpDTO getPreparedClaimExp(Long userId) {
    return (PreparedClaimExpDTO) cache.get(Constants.getAchievementRequirementExpKey(userId));
  }

  public boolean verifyLevelUpLock(Long userId, Long exp) {
    var oExp = cache.get(Constants.getAchievementLevelUpLockKey(userId));
    if (Objects.nonNull(oExp) && String.valueOf(oExp).equals(String.valueOf(exp))) return false;
    cache.set(Constants.getAchievementLevelUpLockKey(userId), exp, 10);
    return true;
  }

  public Boolean offChainVerifyClaimExp(Integer level, Long exp, Long userId) {
    //    if (!verifyLevelUpLock(user.getId(), exp)) {
    //      return false;
    //    }
    var achievementIdAndRequirementId = getPreparedClaimExp(userId);
    if (Objects.isNull(achievementIdAndRequirementId)) {
      throw ControllerUtils.notFound("prepared claim exp not found");
    }
    var achievementId = achievementIdAndRequirementId.getAchievementId();
    var requirementId = achievementIdAndRequirementId.getRequirementId();
    return requirementExpVerify(achievementId, requirementId, exp, userId);
  }

  public Boolean onChainSuccessClaimExp(Integer level, Long exp, String walletAddress) {
    //    var user = userDao
    //        .getUserByWalletAddress(Ascii.toLowerCase(walletAddress))
    //        .orElseThrow(() -> ControllerUtils.notFound("wallet address not found"));
    //    return onChainSuccessClaimExp0(exp, user);
    log.info("ignore onchain success claim exp ${walletAddress} ${level} ${exp}");
    return true;
  }

  public Boolean onChainSuccessClaimExp0(Long exp, User user) {
    var preparedClaimExp = getPreparedClaimExp(user.getId());
    if (Objects.isNull(preparedClaimExp)) {
      log.info("onchain-levelup-success prepared claim exp not found");
      return false;
    }
    if (!Objects.equals(exp, preparedClaimExp.getExp())) {
      log.info("prepared last exp not match ${exp} ${preparedClaimExp.getExp()}");
      return false;
    }
    var achievementId = preparedClaimExp.getAchievementId();
    var requirementId = preparedClaimExp.getRequirementId();
    clearPreparedClaimExp(user);
    log.info("onchain-levelup-success clearPreparedClaimExp");
    return requirementExpSuccess(achievementId, requirementId, exp, user);
  }

  private TransactionReceipt getTransactionReceipt(String transactionHash) {
    var retryTimes = 6;
    for (int i = 0; i < retryTimes; i++) {
      try {
        var receipt = web3j.ethGetTransactionReceipt(transactionHash).send();
        if (receipt.getTransactionReceipt().isPresent()) {
          return receipt.getTransactionReceipt().get();
        }
        log.error("transaction receipt not found {}", transactionHash);
      } catch (Exception e) {
        log.error("transaction receipt get error {}", transactionHash);
      }
      Thread.sleep(2000);
    }
    throw ControllerUtils.notFound("transaction receipt not found");
  }

  public LevelAchievementManager.LevelUpgradedEventResponse readLevelUpgradedEvent(
      String transactionHash) {
    try {
      var transactionReceipt = getTransactionReceipt(transactionHash);

      if ("0x1".equals(transactionReceipt.getStatus())) {
        var logs = transactionReceipt.getLogs();

        var levelUpgradedLog = logs.filter(l -> l.getTopics().size() > 1).findFirst();
        if (levelUpgradedLog.isEmpty()) {
          throw ControllerUtils.badRequest("empty levelUpgraded log");
        }
        return LevelAchievementManager.getLevelUpgradedEventFromLog(levelUpgradedLog.get());
      } else {
        throw ControllerUtils.badRequest(
            "transaction receipt status ${transactionReceipt.getStatus()}");
      }

    } catch (Exception e) {
      throw ControllerUtils.badRequest("readLevelUpgradedEvent error ${e}");
    }
  }

  public Boolean syncClaimExp(Long userId, String hash) {
    var event = readLevelUpgradedEvent(hash);
    var user = userDao
        .getUserById(userId)
        .orElseThrow(() -> ControllerUtils.notFound("wallet address not found"));
    var preparedClaimExp = getPreparedClaimExp(userId);
    if (Objects.isNull(preparedClaimExp)) {
      return false;
    }
    if (!event.user.equalsIgnoreCase(user.getWalletAddress())) {
      throw ControllerUtils.badRequest(
          "incorrect user ${userId} ${user.getWalletAddress()} ${event.user}");
    }
    if (!preparedClaimExp.getLevel().equals(event.level.intValue())) {
      throw ControllerUtils.badRequest(
          "incorrect level ${preparedClaimExp.getLevel()} ${event.level}");
    }
    if (!preparedClaimExp.getExp().equals(event.exp.longValue())) {
      throw ControllerUtils.badRequest("incorrect exp ${preparedClaimExp.getExp()} ${event.exp}");
    }

    var requirement = achievementRequirementDao
        .getById(preparedClaimExp.getRequirementId())
        .orElseThrow(() -> ControllerUtils.notFound("requirement not found"));
    log.info("onChainSuccessClaimExp ${userId} ${requirement.getSort()} ${hash}");
    Boolean success = onChainSuccessClaimExp0(preparedClaimExp.getExp(), user);
    if (!success) {
      throw ControllerUtils.badRequest("claim exp not success");
    }
    return true;
  }

  public Tuple2<List<Long>, Season> getAchievementIdsAndSeasonInProgress() {
    var currentSession = seasonDao
        .current()
        .orElseThrow(() -> ControllerUtils.notFound("current session not found"));

    var achievementIds = achievementDao.getIdInProgressBySeason(currentSession.getId());
    return Tuples.of(achievementIds, currentSession);
  }

  public List<Long> getAchievementIdsAndSeasonClosed() {
    var currentSession = seasonDao
        .current()
        .orElseThrow(() -> ControllerUtils.notFound("current session not found"));

    return achievementDao.getIdClosedBySeason(currentSession.getId());
  }

  public Map<String, Season> getAchievementIdSeasonMap() {
    var map = (Map<String, Season>) cache.get(ACHIEVEMENT_SEASON_DICT);
    if (Objects.nonNull(map)) {
      return map;
    }
    var achievements = getAchievementAll();
    var achievementIds = achievements.map(Achievement::getId).toList();
    var seasonAchievements = seasonAchievementDao.getByAchievementIds(achievementIds);
    if (CollectionUtils.isEmpty(seasonAchievements)) {
      return Map.of();
    }
    var achievementIdSeasonIdMap = seasonAchievements.stream()
        .collect(Collectors.toMap(
            SeasonAchievement::getAchievementId, SeasonAchievement::getSeasonId, (s0, s1) -> s0));
    var seasons = seasonDao.getByIds(achievementIdSeasonIdMap.values());
    var seasonMap = seasons.toMap(Season::getId);

    var achievementIdSeasonMap = seasonAchievements.stream()
        .filter(s -> seasonMap.containsKey(s.getSeasonId()))
        .collect(Collectors.toMap(
            s -> Long.toString(s.getAchievementId()),
            s -> seasonMap.get(s.getSeasonId()),
            (s0, s1) -> s0));

    cache.set(ACHIEVEMENT_SEASON_DICT, achievementIdSeasonMap, 10 * 60);
    return achievementIdSeasonMap;
  }

  public List<SeasonAchievement> getAllSeasonAchievements(Long seasonId) {
    return seasonAchievementDao.getBySeasonId(seasonId);
  }

  public List<SeasonAchievement> getByAchievementId(Long achievementId) {
    return seasonAchievementDao.getByAchievementId(achievementId);
  }

  public SeasonAchievement createSeasonAchievement(Long achievementId, Long seasonId) {
    seasonAchievementDao.create(SeasonAchievement.builder()
        .achievementId(achievementId)
        .seasonId(seasonId)
        .build());
    return null;
  }

  public void deleteSeasonAchievement(Long achievementId, Long seasonId) {
    seasonAchievementDao.delete(achievementId, seasonId);
  }

  public Boolean queryAchievementNFT(String user, Long achievementId) throws IOException {
    OkHttpClient client = new OkHttpClient();
    String query =
        """
            query AchievementTokenMinteds {
              achievementTokenMinteds(
                where: {achievementId: "%s", user: "%s"}
              ) {
                id
                transactionHash
                user
              }
            }
            """
            .formatted(achievementId, user);

    Map<String, Object> requestBody = new HashMap<>();
    requestBody.put("query", query);
    requestBody.put("operationName", "AchievementTokenMinteds");
    requestBody.put("extensions", new HashMap<>());
    String jsonBody = new ObjectMapper().writeValueAsString(requestBody);
    Request request = new Request.Builder()
        .url(graphqlUrl)
        .post(RequestBody.create(jsonBody, MediaType.parse("application/json")))
        .addHeader("accept", "application/graphql-response+json, application/json, multipart/mixed")
        .addHeader("cache-control", "no-cache")
        .addHeader("content-type", "application/json")
        .build();
    Response response = client.newCall(request).execute();
    if (!response.isSuccessful()) {
      return false;
    }
    var objectMapper = new ObjectMapper();
    var qs = objectMapper.readValue(response.body().string(), AchievementTokenMintedsDTO.class);
    return !qs.getData().getAchievementTokenMinteds().isEmpty();
  }
}
