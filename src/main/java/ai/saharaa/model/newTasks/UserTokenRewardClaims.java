package ai.saharaa.model.newTasks;

import ai.saharaa.enums.BaseIntegerEnum;
import ai.saharaa.enums.RewardTokenType;
import com.baomidou.mybatisplus.annotation.EnumValue;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "user_token_reward_claims", autoResultMap = true)
public class UserTokenRewardClaims implements Serializable {
  @TableId(type = IdType.AUTO)
  private Long id;

  private Long userId;

  @JsonProperty
  @JsonSerialize(using = ToStringSerializer.class)
  private BigDecimal amount;

  private RewardTokenType rewardTokenType;
  private TokenClaimStatus status;
  private Long thirdPartyTokenId;
  private String txHash;
  private String network;

  // Withdrawal-related fields
  private String walletAddress;
  private Timestamp freezeEndTime;
  private Boolean requiresReconciliation;
  private Boolean reconciliationPassed;
  private String reconciliationReason;

  private Boolean deleted;
  private Timestamp createdAt;
  private Timestamp updatedAt;

  public enum TokenClaimStatus implements BaseIntegerEnum {
    PENDING(0),
    DISTRIBUTED(1),
    FAILED(2),
    PENDING_REVIEW(3),
    READY_TO_CLAIM(4),
    CLAIMED(5);

    @EnumValue
    @JsonValue
    public final Integer value;

    TokenClaimStatus(Integer value) {
      this.value = value;
    }

    @Override
    public Integer getValue() {
      return value;
    }
  }
}
