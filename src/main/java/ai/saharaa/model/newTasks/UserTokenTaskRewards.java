package ai.saharaa.model.newTasks;

import ai.saharaa.common.typeHandler.PgJacksonTypeHandler;
import ai.saharaa.enums.BaseIntegerEnum;
import ai.saharaa.enums.RewardTokenType;
import com.baomidou.mybatisplus.annotation.*;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonValue;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.type.JdbcType;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "user_token_task_rewards", autoResultMap = true)
public class UserTokenTaskRewards implements Serializable {
  @TableId(type = IdType.AUTO)
  private Long id;

  private Long userId;
  private Long jobId; // task id on chain

  @JsonProperty
  @JsonSerialize(using = ToStringSerializer.class)
  private BigDecimal amount;

  private RewardTokenType rewardTokenType;
  private Long thirdPartyTokenId;
  private UserRewardStatus status;

  @TableField(typeHandler = PgJacksonTypeHandler.class, jdbcType = JdbcType.OTHER)
  private List<String> merkleProof;

  private Boolean deleted;
  private Timestamp createdAt;
  private Timestamp updatedAt;

  public enum UserRewardStatus implements BaseIntegerEnum {
    DEFAULT(0),
    PENDING_CLAIM(2),
    WITHDRAW_DONE(1);

    @EnumValue
    @JsonValue
    final Integer value;

    UserRewardStatus(Integer value) {
      this.value = value;
    }

    @Override
    public Integer getValue() {
      return value;
    }
  }
}
